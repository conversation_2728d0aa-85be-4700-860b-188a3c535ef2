<?php

namespace App\Http\Controllers\User;

use Illuminate\Http\Request;
use App\Services\User\OrderService;
use App\Http\Controllers\Controller;
use App\Http\Requests\User\Order\StoreRequest;

class OrderController extends Controller
{
    public function __construct(private readonly OrderService $orderService)
    {
        //
    }

    public function index()
    {
        //
    }

    public function store(StoreRequest $request)
    {
        dd(auth('user')->id());

        $this->orderService->create($request->validated());
    }

    public function show(string $id)
    {
        //
    }

    public function update(Request $request, string $id)
    {
        //
    }

    public function destroy(string $id)
    {
        //
    }
}
