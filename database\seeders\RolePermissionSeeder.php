<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class RolePermissionSeeder extends Seeder
{
    public function run(): void
    {
        // Admin Permissions
        Permission::create(['name' => 'show admins', 'type' => 'admin', 'group' => 'admins']);
        Permission::create(['name' => 'show admin', 'type' => 'admin', 'group' => 'admins']);
        Permission::create(['name' => 'create admin', 'type' => 'admin', 'group' => 'admins']);
        Permission::create(['name' => 'update admin', 'type' => 'admin', 'group' => 'admins']);
        Permission::create(['name' => 'delete admin', 'type' => 'admin', 'group' => 'admins']);

        Permission::create(['name' => 'show users', 'type' => 'admin', 'group' => 'admins']);
        Permission::create(['name' => 'show user', 'type' => 'admin', 'group' => 'admins']);
        Permission::create(['name' => 'create user', 'type' => 'admin', 'group' => 'admins']);
        Permission::create(['name' => 'update user', 'type' => 'admin', 'group' => 'admins']);
        Permission::create(['name' => 'delete user', 'type' => 'admin', 'group' => 'admins']);

        // Company Admin Permissions

        Permission::create(['name' => 'show admins', 'type' => 'company', 'group' => 'admins']);
        Permission::create(['name' => 'show admin', 'type' => 'company', 'group' => 'admins']);
        Permission::create(['name' => 'create admin', 'type' => 'company', 'group' => 'admins']);
        Permission::create(['name' => 'update admin', 'type' => 'company', 'group' => 'admins']);
        Permission::create(['name' => 'delete admin', 'type' => 'company', 'group' => 'admins']);

        Permission::create(['name' => 'show drivers', 'type' => 'company', 'group' => 'drivers']);
        Permission::create(['name' => 'show driver', 'type' => 'company', 'group' => 'drivers']);
        Permission::create(['name' => 'create driver', 'type' => 'company', 'group' => 'drivers']);
        Permission::create(['name' => 'update driver', 'type' => 'company', 'group' => 'drivers']);
        Permission::create(['name' => 'delete driver', 'type' => 'company', 'group' => 'drivers']);


        // Main Roles

        // Admin Dashboard
        $super_admin = Role::create(['name' => 'super admin', 'type' => 'admin']);
        $admin_supervisor = Role::create(['name' => 'supervisor', 'type' => 'admin']);

        // Company Dashboard
        $super_company_admin = Role::create(['name' => 'super admin', 'type' => 'company']);
        $company_supervisor = Role::create(['name' => 'supervisor', 'type' => 'company']);

        // Asigning Permission To Roles

        // Admin Permissions
        $all_admin_permissions = Permission::where('type', 'admin')->get()->pluck('id')->toArray();
        $admin_supervisor_permissions = Permission::where('name', 'NOT LIKE', "%admin%")->where('type', 'admin')->get()->pluck('id')->toArray();

        $super_admin->permissions()->attach($all_admin_permissions);
        $admin_supervisor->permissions()->attach($admin_supervisor_permissions);

        // Vendor Admin Permissions
        $all_company_permissions = Permission::where('type', 'company')->get()->pluck('id')->toArray();
        $company_supervisor_permissions = Permission::where('name', 'NOT LIKE', "%admin%")->where('type', 'company')->get()->pluck('id')->toArray();

        $super_company_admin->permissions()->attach($all_company_permissions);
        $company_supervisor->permissions()->attach($company_supervisor_permissions);
    }
}
