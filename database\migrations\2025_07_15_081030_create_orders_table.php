<?php

use App\Enum\OrderStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained();
            $table->foreignId('driver_id')->nullable()->constrained();
            $table->foreignId('shipping_type_id')->constrained();
            $table->foreignId('shipping_size_id')->constrained();
            $table->foreignId('pickup_address_id')->nullable()->constrained('addresses');
            $table->geometry('pickup_location', subtype: 'point')->nullable();
            $table->string('payment_method');
            $table->string('status')->default(OrderStatus::READY_TO_PICKUP->value);
            $table->boolean('is_express')->default(false);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
