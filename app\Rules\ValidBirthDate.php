<?php

namespace App\Rules;

use Carbon\Carbon;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidBirthDate implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Strict regex: YYYY-MM-DD with year between 1900 and 2100
        if (!preg_match('/^(19\d{2}|20\d{2}|2100)-\d{2}-\d{2}$/', $value)) {
            $fail("The :attribute must be a valid date between 1900 and 2100 in Y-m-d format.");
            return;
        }

        try {
            $date = Carbon::createFromFormat('Y-m-d', $value);

            $eighteenYearsAgo = Carbon::now()->subYears(18);

            if ($date->greaterThan($eighteenYearsAgo)) {
                $fail("The :attribute must be at least 18 years ago.");
            }
        } catch (\Exception $e) {
            $fail("The :attribute must be a valid date.");
        }
    }
}
