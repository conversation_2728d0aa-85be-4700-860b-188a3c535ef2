<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use <PERSON>vel\Sanctum\HasApiTokens;

class User extends Model
{
    use SoftDeletes, HasApiTokens;
    
    protected $fillable = [
        'name',
        'country_code',
        'phone',
        'email',
        'birth_date',
        'gender',
        'status',
        'last_login',
        'push_notifications_enabled',
    ];

    protected $casts = [
        'birth_date' => 'date',
        'last_login' => 'datetime',
        'push_notifications_enabled' => 'boolean',
    ];


    public function getPhoneAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function getEmailAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function image()
    {
        return $this->morphOne(Media::class, 'mediable');
    }
}
