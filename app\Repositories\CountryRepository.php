<?php

namespace App\Repositories;

use App\Models\Country;

class CountryRepository
{
    public function __construct(private Country $model) {}

    public function getById(string $id)
    {
        return $this->model->find($id);
    }

    public function getByCode(string $code)
    {
        return $this->model->where('code', $code)->first();
    }

    public function getAll()
    {
        return $this->model->withTranslation()->get();
    }
}
