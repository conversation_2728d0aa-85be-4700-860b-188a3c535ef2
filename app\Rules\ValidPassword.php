<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidPassword implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (strlen($value) < 8) {
            $fail(__('validation.min.string', ['attribute' => __('attributes.password'), 'min' => 8]));
        }

        if (strlen($value) > 40) {
            $fail(__('validation.min.string', ['attribute' => __('attributes.password'), 'max' => 40]));
        }

        if (
            !preg_match('/[a-z]/', $value) ||
            !preg_match('/[A-Z]/', $value) ||
            !preg_match('/[0-9]/', $value) ||
            !preg_match('/[\W_]/', $value)
        ) {
            $fail(__('validation.password.mixed', ['attribute' => __('attributes.password')]));
        }
    }
}
