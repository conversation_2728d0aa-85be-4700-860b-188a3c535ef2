<?php

namespace App\Repositories;

use App\Models\City;

class CityRepository
{
    public function __construct(private City $model) {}

    public function getById(string $id)
    {
        return $this->model->find($id);
    }

    public function getByIds(array $ids)
    {
        return $this->model->whereIn('id', $ids)->get();
    }

    public function getActiveCities(?bool $with_areas)
    {
        return $this->model
            ->where('status', 'active')->withTranslation()
            ->when($with_areas, function ($query) {
                $query->with(['areas' => function ($q) {
                    $q->where('status', 'active')->withTranslation();
                }]);
            })
            ->get();
    }
}
