<?php

namespace App\Repositories;

use App\Models\City;
use MatanYadaev\EloquentSpatial\Objects\Point;

class CityRepository
{
    public function __construct(private City $model) {}

    public function getById(string $id)
    {
        return $this->model->find($id);
    }

    public function getByIds(array $ids)
    {
        return $this->model->whereIn('id', $ids)->get();
    }

    public function getActiveCities(?bool $with_areas)
    {
        return $this->model
            ->where('status', 'active')->withTranslation()
            ->when($with_areas, function ($query) {
                $query->with(['areas' => function ($q) {
                    $q->where('status', 'active')->withTranslation();
                }]);
            })
            ->get();
    }

    public function getByLocation(float $lat, float $lng)
    {
        // Create a Point object from the given coordinates
        // Note: Point constructor takes (latitude, longitude) in that order
        $point = new Point($lat, $lng);

        // First try using the whereContains scope from HasSpatial trait
        $city = $this->model
            ->where('status', 'active')
            ->whereContains('area', $point)
            ->withTranslation()
            ->first();

        if ($city) {
            return $city;
        }

        // If that doesn't work, try the raw SQL approach
        // Note: In MySQL, the POINT function takes longitude first, then latitude
        try {
            $city = $this->model
                ->where('status', 'active')
                ->whereRaw('ST_Contains(area, ST_GeomFromText(?))', ["POINT($lng $lat)"])
                ->withTranslation()
                ->first();

            if ($city) {
                return $city;
            }
        } catch (\Exception $e) {
            // If raw SQL fails, continue to return null
        }

        return null;
    }

    public function isPointInAnyCityPolygon(float $lat, float $lng): array
    {
        // Create a Point object from the given coordinates
        // Note: Point constructor takes (latitude, longitude) in that order
        $point = new Point($lat, $lng);

        // First try using the whereContains scope from HasSpatial trait
        $city = $this->model
            ->where('status', 'active')
            ->whereContains('area', $point)
            ->withTranslation()
            ->first();

        if ($city) {
            return [
                'inside' => true,
                'city' => $city
            ];
        }

        // If that doesn't work, try the raw SQL approach
        // Note: In MySQL, the POINT function takes longitude first, then latitude
        try {
            $city = $this->model
                ->where('status', 'active')
                ->whereRaw('ST_Contains(area, ST_GeomFromText(?))', ["POINT($lng $lat)"])
                ->withTranslation()
                ->first();

            if ($city) {
                return [
                    'inside' => true,
                    'city' => $city
                ];
            }
        } catch (\Exception $e) {
            // If raw SQL fails, continue to return false
        }

        return [
            'inside' => false,
            'city' => null
        ];
    }
}
