<?php

namespace App\Repositories;

use App\Models\City;
use MatanYadaev\EloquentSpatial\Objects\Point;

class CityRepository
{
    public function __construct(private City $model) {}

    public function getById(string $id)
    {
        return $this->model->find($id);
    }

    public function getByIds(array $ids)
    {
        return $this->model->whereIn('id', $ids)->get();
    }

    public function getActiveCities(?bool $with_areas)
    {
        return $this->model
            ->where('status', 'active')->withTranslation()
            ->when($with_areas, function ($query) {
                $query->with(['areas' => function ($q) {
                    $q->where('status', 'active')->withTranslation();
                }]);
            })
            ->get();
    }

    public function getByLocation(float $lat, float $lng)
    {
        $point = new Point($lat, $lng);

        dd($point);

        return $this->model
            ->where('status', 'active')
            ->whereContains('area', $point)
            ->withTranslation()
            ->first();
    }
}
