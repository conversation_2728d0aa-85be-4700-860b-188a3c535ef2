@media (max-width: 576px) {

    h3,
    .h3 {
        font-size: 16px;
    }

    .ti {
        vertical-align: middle;
        font-size: 22px !important;
        line-height: 1;
        display: inline-block;
    }

    .card-heading a {
        font-size: 12px;
    }

    div.dataTables_wrapper .dataTables_filter {
        justify-content: flex-start !important;
        margin-top: 20px;
    }

    div.dataTables_length {
        text-align: right !important;
    }
}

.datatables-basic th,
td {
    text-align: center !important;
}

.card {
    padding: 20px
}

.card-heading {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 1.5rem;
    flex-wrap: wrap;
}

.card-heading h3 {
    margin-top: 15px;
}

.custom-table th,
.custom-table td {
    text-align: center !important;
    vertical-align: middle;
}

.custom-table th {
    font-weight: bold;
}

.custom--table td {
    font-size: 14px;
}

.ti {
    font-size: 1.60rem;
}

td .operations {
    display: flex;
    gap: 20px;
}

table i.ti-eye {
    color: #ffc107;
}

table i.ti-edit {
    color: #6c757d;
}

table i.ti-archive {
    color: #dc3545;
}

/* style for show page */

.list-group-item-fixed {
    width: auto;
}

.list-group-left {
    text-align: right;
    width: 200px;
    font-weight: bold;
    display: inline-block;
}

.list-group-right {
    text-align: right;
    display: inline-block;
}
@media (max-width: 575px) {
    .card-heading{
        display: block !important;
    }
    .waves-effect {
        width: 100% !important;
    }
    .waves-effect:not(:last-of-type)     {
        margin-bottom: 10px !important;
    }   
}

table.dataTable.table-striped > tbody > tr > td{
    min-width: 90px;
}

.home-table table.table td{
    width: max-content;
}

.custom-card-body .card-body{
    overflow: auto;
}

.custom-table .operations{
    justify-content: center;
}

.spinner-tabler {
    animation: spin 1s linear infinite;
    vertical-align: middle;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.dataTables_wrapper {
    position: relative; /* Makes .dataTables_processing position relative to the table */
}

.dataTables_processing {
    position: absolute !important;
    top: 5% !important; /* This is the key: place it 10% from the top of the table */
    left: 55% !important;
    transform: translateX(-50%) !important;
    z-index: 100 !important;
    background: rgba(255, 255, 255, 0.9);
    padding: 15px 30px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
    text-align: center;
}