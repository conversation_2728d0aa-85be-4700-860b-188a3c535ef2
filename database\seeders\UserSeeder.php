<?php

namespace Database\Seeders;

use App\Models\Country;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        for ($i = 1; $i <= 5; $i++) {
            $country = Country::find(fake()->randomElement([1, 2]));

            User::Create([
                'name' => "User $i",
                'country_id' => $country->id,
                'country_code' => $country->code,
                'phone' => "101234562$i",
                'email' => "user$<EMAIL>",
                'birth_date' => fake()->date(),
                'gender' => fake()->randomElement(['male', 'female']),
            ]);
        }
    }
}
