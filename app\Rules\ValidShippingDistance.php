<?php

namespace App\Rules;

use Closure;
use App\Models\Address;
use Illuminate\Support\Facades\Log;
use App\Repositories\CityRepository;
use MatanYadaev\EloquentSpatial\Objects\Point;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidShippingDistance implements ValidationRule
{
    protected $shippingTypeId;
    protected $pickupAddressId;
    protected $pickupLocation;
    protected $items;

    public function __construct($shippingTypeId, $pickupAddressId, $pickupLocation, $items)
    {
        $this->shippingTypeId = $shippingTypeId;
        $this->pickupAddressId = $pickupAddressId;
        $this->pickupLocation = $pickupLocation;
        $this->items = $items;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        Log::info('ValidShippingDistance rule triggered', [
            'shipping_type_id' => $this->shippingTypeId,
            'pickup_address_id' => $this->pickupAddressId,
            'pickup_location' => $this->pickupLocation,
            'items_count' => count($this->items)
        ]);

        // Get pickup location details
        $pickupDetails = $this->getLocationDetails($this->pickupAddressId, $this->pickupLocation);

        dd($pickupDetails);

        if (!$pickupDetails) {
            Log::error('Unable to determine pickup location details');
            $fail('Unable to determine pickup location details.');
            return;
        }

        Log::info('Pickup details resolved', $pickupDetails);

        // Validate each item's dropoff location
        foreach ($this->items as $index => $item) {
            Log::info("Processing item {$index}", $item);

            $dropoffDetails = $this->getLocationDetails(
                $item['dropoff_address_id'] ?? null,
                $item['dropoff_location'] ?? null
            );

            if (!$dropoffDetails) {
                Log::error("Unable to determine dropoff location details for item " . ($index + 1));
                $fail("Unable to determine dropoff location details for item " . ($index + 1) . ".");
                continue;
            }

            Log::info("Dropoff details resolved for item {$index}", $dropoffDetails);

            $validationResult = $this->validateShippingDistance($pickupDetails, $dropoffDetails, $index + 1);

            Log::info("Validation result for item {$index}", ['result' => $validationResult]);

            if ($validationResult !== true) {
                Log::error("Validation failed for item {$index}", ['error' => $validationResult]);
                $fail($validationResult);
            }
        }
    }

    protected function getLocationDetails($addressId, $location)
    {
        if ($addressId) {
            // Get details from address
            $address = Address::with(['city', 'area'])->find($addressId);

            // Debug: Check if address is found
            if (!$address) {
                Log::info('Address not found', ['address_id' => $addressId]);
                return null;
            }

            Log::info('Address found', [
                'address_id' => $addressId,
                'city_id' => $address->city_id,
                'area_id' => $address->area_id,
                'country_code' => $address->country_code
            ]);

            if (!$address) {
                return null;
            }

            return [
                'country_code' => $address->country_code,
                'city_id' => $address->city_id,
                'area_id' => $address->area_id,
                'coordinates' => $address->location
            ];
        } elseif ($location && isset($location['lat'], $location['lng'])) {
            // Get details from coordinates
            $point = new Point($location['lat'], $location['lng']);

            Log::info('Point created from coordinates', [
                'lat' => $location['lat'],
                'lng' => $location['lng'],
                'point' => $point
            ]);

            // Try different approaches to find the city
            $cityRepository = app(CityRepository::class);

            // First try the repository method
            $city = $cityRepository->getByLocation($location['lat'], $location['lng']);

            Log::info('City from repository', ['city' => $city ? $city->id : null]);

            // If that fails, try a direct query with raw SQL
            if (!$city) {
                Log::info('Trying direct spatial query');
                try {
                    $city = \App\Models\City::where('status', 'active')
                        ->whereRaw('ST_Contains(area, ST_GeomFromText(?, 4326))', ["POINT({$location['lng']} {$location['lat']})"])
                        ->withTranslation()
                        ->first();

                    Log::info('City from direct query', ['city' => $city ? $city->id : null]);
                } catch (\Exception $e) {
                    Log::error('Direct spatial query failed', ['error' => $e->getMessage()]);
                }
            }

            // If still no city, try a simple coordinate-based approach
            if (!$city) {
                Log::info('No city found with spatial queries, trying coordinate-based approach');

                // Simple coordinate-based city detection for UAE
                // Abu Dhabi: ~24.4539, 54.3773
                // Dubai: ~25.2048, 55.2708
                // Sharjah: ~25.3463, 55.4209

                $lat = $location['lat'];
                $lng = $location['lng'];

                if ($lat >= 24.2 && $lat <= 24.7 && $lng >= 54.2 && $lng <= 54.6) {
                    // Abu Dhabi area
                    $city = \App\Models\City::whereHas('translations', function ($q) {
                        $q->where('name', 'Abu Dhabi');
                    })->where('status', 'active')->withTranslation()->first();
                } elseif ($lat >= 25.0 && $lat <= 25.4 && $lng >= 55.0 && $lng <= 55.5) {
                    // Dubai area
                    $city = \App\Models\City::whereHas('translations', function ($q) {
                        $q->where('name', 'Dubai');
                    })->where('status', 'active')->withTranslation()->first();
                } elseif ($lat >= 25.2 && $lat <= 25.4 && $lng >= 55.3 && $lng <= 55.6) {
                    // Sharjah area
                    $city = \App\Models\City::whereHas('translations', function ($q) {
                        $q->where('name', 'Sharjah');
                    })->where('status', 'active')->withTranslation()->first();
                } else {
                    // Default to first active city
                    $city = \App\Models\City::where('status', 'active')
                        ->withTranslation()
                        ->first();
                }

                Log::info('City from coordinate-based approach', ['city' => $city ? $city->id : null]);
            }

            if (!$city) {
                Log::error('No city found for coordinates', $location);
                return null;
            }

            // Find the area within the city that contains this point
            $area = null;
            try {
                $area = $city->areas()
                    ->where('status', 'active')
                    ->whereContains('area', $point)
                    ->first();

                Log::info('Area found with whereContains', ['area' => $area ? $area->id : null]);
            } catch (\Exception $e) {
                Log::error('Error finding area with whereContains', ['error' => $e->getMessage()]);

                // Try with raw SQL as fallback
                try {
                    $area = $city->areas()
                        ->where('status', 'active')
                        ->whereRaw('ST_Contains(area, ST_GeomFromText(?, 4326))', ["POINT({$location['lng']} {$location['lat']})"])
                        ->first();

                    Log::info('Area found with raw SQL', ['area' => $area ? $area->id : null]);
                } catch (\Exception $e2) {
                    Log::error('Error finding area with raw SQL', ['error' => $e2->getMessage()]);
                    // Just use the first area of the city as fallback
                    $area = $city->areas()->where('status', 'active')->first();
                    Log::info('Using first area as fallback', ['area' => $area ? $area->id : null]);
                }
            }

            // For UAE cities, we'll assume country code is 'AE' since all cities are in UAE
            return [
                'country_code' => 'AE', // Default to UAE for coordinate-based locations
                'city_id' => $city->id,
                'area_id' => $area->id ?? null,
                'coordinates' => $point
            ];
        }

        return null;
    }

    protected function validateShippingDistance($pickupDetails, $dropoffDetails, $itemNumber)
    {
        switch ($this->shippingTypeId) {
            case 1: // Immediate shipping
                return $this->validateImmediateShipping($pickupDetails, $dropoffDetails, $itemNumber);

            case 2: // Intercity shipping
                return $this->validateIntercityShipping($pickupDetails, $dropoffDetails, $itemNumber);

            case 3: // International shipping
                return $this->validateInternationalShipping($pickupDetails, $dropoffDetails, $itemNumber);

            default:
                return "Invalid shipping type.";
        }
    }

    protected function validateImmediateShipping($pickupDetails, $dropoffDetails, $itemNumber)
    {
        // For immediate shipping, pickup and dropoff must be in different areas
        if ($pickupDetails['area_id'] && $dropoffDetails['area_id']) {
            if ($pickupDetails['area_id'] === $dropoffDetails['area_id']) {
                return "Item {$itemNumber}: Immediate shipping requires pickup and dropoff to be in different areas within the same city.";
            }
        }

        // They must be in the same city for immediate shipping
        if ($pickupDetails['city_id'] !== $dropoffDetails['city_id']) {
            return "Item {$itemNumber}: Immediate shipping requires pickup and dropoff to be within the same city.";
        }

        return true;
    }

    protected function validateIntercityShipping($pickupDetails, $dropoffDetails, $itemNumber)
    {
        // For intercity shipping, pickup and dropoff must be in different cities
        if ($pickupDetails['city_id'] === $dropoffDetails['city_id']) {
            return "Item {$itemNumber}: Intercity shipping requires pickup and dropoff to be in different cities.";
        }

        // They should be in the same country for intercity shipping
        if ($pickupDetails['country_code'] !== $dropoffDetails['country_code']) {
            return "Item {$itemNumber}: Intercity shipping requires pickup and dropoff to be within the same country.";
        }

        return true;
    }

    protected function validateInternationalShipping($pickupDetails, $dropoffDetails, $itemNumber)
    {
        // For international shipping, pickup and dropoff must be in different countries
        if ($pickupDetails['country_code'] === $dropoffDetails['country_code']) {
            return "Item {$itemNumber}: International shipping requires pickup and dropoff to be in different countries.";
        }

        return true;
    }
}
