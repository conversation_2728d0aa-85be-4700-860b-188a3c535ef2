<?php

namespace App\Rules;

use Closure;
use App\Models\Address;
use App\Models\City;
use App\Repositories\CityRepository;
use MatanYadaev\EloquentSpatial\Objects\Point;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidShippingDistance implements ValidationRule
{
    protected int $shippingTypeId;
    protected ?int $pickupAddressId;
    protected ?array $pickupLocation;
    protected array $items;

    public function __construct($shippingTypeId, $pickupAddressId, $pickupLocation, $items)
    {
        $this->shippingTypeId = $shippingTypeId;
        $this->pickupAddressId = $pickupAddressId;
        $this->pickupLocation = $pickupLocation;
        $this->items = $items;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $pickupDetails = $this->getLocationDetails($this->pickupAddressId, $this->pickupLocation);

        if (!$pickupDetails) {
            $fail('Unable to determine pickup location details.');
            return;
        }

        foreach ($this->items as $index => $item) {
            $dropoffDetails = $this->getLocationDetails(
                $item['dropoff_address_id'] ?? null,
                $item['dropoff_location'] ?? null
            );

            if (!$dropoffDetails) {
                $fail("Unable to determine dropoff location details for item " . ($index + 1) . ".");
                continue;
            }

            $validationResult = $this->validateShippingDistance($pickupDetails, $dropoffDetails, $index + 1);

            if ($validationResult !== true) {
                $fail($validationResult);
            }
        }
    }

    protected function getLocationDetails(?int $addressId, ?array $location): ?array
    {
        if ($addressId) {
            $address = Address::with(['city', 'area'])->find($addressId);

            if (!$address) {
                return null;
            }

            return [
                'country_code' => $address->country_code,
                'city_id' => $address->city_id,
                'area_id' => $address->area_id,
                'coordinates' => $address->location
            ];
        }

        if ($location && isset($location['lat'], $location['lng'])) {
            $lat = $location['lat'];
            $lng = $location['lng'];
            $point = new Point($lat, $lng);

            $city = $this->findCityByCoordinates($lat, $lng);

            if (!$city) {
                return null;
            }

            $area = $this->findAreaInCity($city, $lat, $lng, $point);

            return [
                'country_code' => 'AE',
                'city_id' => $city->id,
                'area_id' => $area?->id,
                'coordinates' => $point
            ];
        }

        return null;
    }

    protected function findCityByCoordinates(float $lat, float $lng): ?City
    {
        $cityRepository = app(CityRepository::class);
        $city = $cityRepository->getByLocation($lat, $lng);
        return $city;
    }

    protected function findAreaInCity(City $city, Point $point): ?\App\Models\Area
    {
        return $city->areas()
            ->where('status', 'active')
            ->whereContains('area', $point)
            ->first();
    }

    protected function validateShippingDistance(array $pickupDetails, array $dropoffDetails, int $itemNumber): string|bool
    {
        switch ($this->shippingTypeId) {
            case 1: // Immediate shipping
                return $this->validateImmediateShipping($pickupDetails, $dropoffDetails, $itemNumber);

            case 2: // Intercity shipping
                return $this->validateIntercityShipping($pickupDetails, $dropoffDetails, $itemNumber);

            case 3: // International shipping
                return $this->validateInternationalShipping($pickupDetails, $dropoffDetails, $itemNumber);

            default:
                return "Invalid shipping type.";
        }
    }

    protected function validateImmediateShipping(array $pickupDetails, array $dropoffDetails, int $itemNumber): string|bool
    {
        // Must be in the same city
        if ($pickupDetails['city_id'] !== $dropoffDetails['city_id']) {
            return "Item {$itemNumber}: Immediate shipping requires pickup and dropoff to be within the same city.";
        }

        // Must be in different areas
        if (
            $pickupDetails['area_id'] && $dropoffDetails['area_id'] &&
            $pickupDetails['area_id'] === $dropoffDetails['area_id']
        ) {
            return "Item {$itemNumber}: Immediate shipping requires pickup and dropoff to be in different areas within the same city.";
        }

        return true;
    }

    protected function validateIntercityShipping(array $pickupDetails, array $dropoffDetails, int $itemNumber): string|bool
    {
        // Must be in different cities
        if ($pickupDetails['city_id'] === $dropoffDetails['city_id']) {
            return "Item {$itemNumber}: Intercity shipping requires pickup and dropoff to be in different cities.";
        }

        // Must be in the same country
        if ($pickupDetails['country_code'] !== $dropoffDetails['country_code']) {
            return "Item {$itemNumber}: Intercity shipping requires pickup and dropoff to be within the same country.";
        }

        return true;
    }

    protected function validateInternationalShipping(array $pickupDetails, array $dropoffDetails, int $itemNumber): string|bool
    {
        // Must be in different countries
        if ($pickupDetails['country_code'] === $dropoffDetails['country_code']) {
            return "Item {$itemNumber}: International shipping requires pickup and dropoff to be in different countries.";
        }

        return true;
    }
}
