<?php

namespace App\Rules;

use Closure;
use App\Models\Address;
use Illuminate\Support\Facades\Log;
use App\Repositories\CityRepository;
use MatanYadaev\EloquentSpatial\Objects\Point;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidShippingDistance implements ValidationRule
{
    protected $shippingTypeId;
    protected $pickupAddressId;
    protected $pickupLocation;
    protected $items;

    public function __construct($shippingTypeId, $pickupAddressId, $pickupLocation, $items)
    {
        $this->shippingTypeId = $shippingTypeId;
        $this->pickupAddressId = $pickupAddressId;
        $this->pickupLocation = $pickupLocation;
        $this->items = $items;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        Log::info('ValidShippingDistance rule triggered', [
            'shipping_type_id' => $this->shippingTypeId,
            'pickup_address_id' => $this->pickupAddressId,
            'pickup_location' => $this->pickupLocation,
            'items_count' => count($this->items)
        ]);

        // Get pickup location details
        $pickupDetails = $this->getLocationDetails($this->pickupAddressId, $this->pickupLocation);

        if (!$pickupDetails) {
            Log::error('Unable to determine pickup location details');
            $fail('Unable to determine pickup location details.');
            return;
        }

        Log::info('Pickup details resolved', $pickupDetails);

        // Validate each item's dropoff location
        foreach ($this->items as $index => $item) {
            Log::info("Processing item {$index}", $item);

            $dropoffDetails = $this->getLocationDetails(
                $item['dropoff_address_id'] ?? null,
                $item['dropoff_location'] ?? null
            );

            if (!$dropoffDetails) {
                Log::error("Unable to determine dropoff location details for item " . ($index + 1));
                $fail("Unable to determine dropoff location details for item " . ($index + 1) . ".");
                continue;
            }

            Log::info("Dropoff details resolved for item {$index}", $dropoffDetails);

            $validationResult = $this->validateShippingDistance($pickupDetails, $dropoffDetails, $index + 1);

            Log::info("Validation result for item {$index}", ['result' => $validationResult]);

            if ($validationResult !== true) {
                Log::error("Validation failed for item {$index}", ['error' => $validationResult]);
                $fail($validationResult);
            }
        }
    }

    protected function getLocationDetails($addressId, $location)
    {
        if ($addressId) {
            // Get details from address
            $address = Address::with(['city', 'area'])->find($addressId);

            // Debug: Check if address is found
            if (!$address) {
                Log::info('Address not found', ['address_id' => $addressId]);
                return null;
            }

            Log::info('Address found', [
                'address_id' => $addressId,
                'city_id' => $address->city_id,
                'area_id' => $address->area_id,
                'country_code' => $address->country_code
            ]);

            if (!$address) {
                return null;
            }

            return [
                'country_code' => $address->country_code,
                'city_id' => $address->city_id,
                'area_id' => $address->area_id,
                'coordinates' => $address->location
            ];
        } elseif ($location && isset($location['lat'], $location['lng'])) {
            // Get details from coordinates
            $point = new Point($location['lat'], $location['lng']);

            Log::info('Point created from coordinates', [
                'lat' => $location['lat'],
                'lng' => $location['lng'],
                'point' => $point
            ]);

            $cityRepository = app(CityRepository::class);
            $city = $cityRepository->getByLocation($location['lat'], $location['lng']);

            if (!$city) {
                return null;
            }

            // Find the area within the city that contains this point
            $area = $city->areas()
                ->where('status', 'active')
                ->whereContains('area', $point)
                ->first();

            // For UAE cities, we'll assume country code is 'AE' since all cities are in UAE
            return [
                'country_code' => 'AE', // Default to UAE for coordinate-based locations
                'city_id' => $city->id,
                'area_id' => $area->id ?? null,
                'coordinates' => $point
            ];
        }

        return null;
    }

    protected function validateShippingDistance($pickupDetails, $dropoffDetails, $itemNumber)
    {
        switch ($this->shippingTypeId) {
            case 1: // Immediate shipping
                return $this->validateImmediateShipping($pickupDetails, $dropoffDetails, $itemNumber);

            case 2: // Intercity shipping
                return $this->validateIntercityShipping($pickupDetails, $dropoffDetails, $itemNumber);

            case 3: // International shipping
                return $this->validateInternationalShipping($pickupDetails, $dropoffDetails, $itemNumber);

            default:
                return "Invalid shipping type.";
        }
    }

    protected function validateImmediateShipping($pickupDetails, $dropoffDetails, $itemNumber)
    {
        // For immediate shipping, pickup and dropoff must be in different areas
        if ($pickupDetails['area_id'] && $dropoffDetails['area_id']) {
            if ($pickupDetails['area_id'] === $dropoffDetails['area_id']) {
                return "Item {$itemNumber}: Immediate shipping requires pickup and dropoff to be in different areas within the same city.";
            }
        }

        // They must be in the same city for immediate shipping
        if ($pickupDetails['city_id'] !== $dropoffDetails['city_id']) {
            return "Item {$itemNumber}: Immediate shipping requires pickup and dropoff to be within the same city.";
        }

        return true;
    }

    protected function validateIntercityShipping($pickupDetails, $dropoffDetails, $itemNumber)
    {
        // For intercity shipping, pickup and dropoff must be in different cities
        if ($pickupDetails['city_id'] === $dropoffDetails['city_id']) {
            return "Item {$itemNumber}: Intercity shipping requires pickup and dropoff to be in different cities.";
        }

        // They should be in the same country for intercity shipping
        if ($pickupDetails['country_code'] !== $dropoffDetails['country_code']) {
            return "Item {$itemNumber}: Intercity shipping requires pickup and dropoff to be within the same country.";
        }

        return true;
    }

    protected function validateInternationalShipping($pickupDetails, $dropoffDetails, $itemNumber)
    {
        // For international shipping, pickup and dropoff must be in different countries
        if ($pickupDetails['country_code'] === $dropoffDetails['country_code']) {
            return "Item {$itemNumber}: International shipping requires pickup and dropoff to be in different countries.";
        }

        return true;
    }
}
