<?php

namespace App\Rules;

use Closure;
use App\Models\Address;
use App\Models\City;
use App\Repositories\CityRepository;
use MatanYadaev\EloquentSpatial\Objects\Point;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidShippingDistance implements ValidationRule
{
    protected int $shippingTypeId;
    protected ?int $pickupAddressId;
    protected ?array $pickupLocation;
    protected array $items;

    public function __construct($shippingTypeId, $pickupAddressId, $pickupLocation, $items)
    {
        $this->shippingTypeId = $shippingTypeId;
        $this->pickupAddressId = $pickupAddressId;
        $this->pickupLocation = $pickupLocation;
        $this->items = $items;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $pickupDetails = $this->getLocationDetails($this->pickupAddressId, $this->pickupLocation);

        if (!$pickupDetails) {
            $fail('Unable to determine pickup location details.');
            return;
        }

        foreach ($this->items as $index => $item) {
            $dropoffDetails = $this->getLocationDetails(
                $item['dropoff_address_id'] ?? null,
                $item['dropoff_location'] ?? null
            );

            if (!$dropoffDetails) {
                $fail("Unable to determine dropoff location details for item " . ($index + 1) . ".");
                continue;
            }

            $validationResult = $this->validateShippingDistance($pickupDetails, $dropoffDetails, $index + 1);

            if ($validationResult !== true) {
                $fail($validationResult);
            }
        }
    }

    protected function getLocationDetails(?int $addressId, ?array $location): ?array
    {
        if ($addressId) {
            $address = Address::with(['city', 'area'])->find($addressId);

            if (!$address) {
                return null;
            }

            return [
                'country_code' => $address->country_code,
                'city_id' => $address->city_id,
                'area_id' => $address->area_id,
                'coordinates' => $address->location
            ];
        }

        if ($location && isset($location['lat'], $location['lng'])) {
            $lat = $location['lat'];
            $lng = $location['lng'];
            $point = new Point($lat, $lng);

            $city = $this->findCityByCoordinates($lat, $lng);

            if (!$city) {
                return null;
            }

            $area = $this->findAreaInCity($city, $lat, $lng, $point);

            return [
                'country_code' => 'AE',
                'city_id' => $city->id,
                'area_id' => $area?->id,
                'coordinates' => $point
            ];
        }

        return null;
    }

    protected function findCityByCoordinates(float $lat, float $lng): ?City
    {
        // Try repository method with detailed response
        $cityRepository = app(CityRepository::class);
        $result = $cityRepository->isPointInAnyCityPolygon($lat, $lng);

        if ($result['inside'] && $result['city']) {
            return $result['city'];
        }

        // Simple coordinate-based fallback for UAE cities
        // Abu Dhabi: ~24.4539, 54.3773
        if ($lat >= 24.2 && $lat <= 24.7 && $lng >= 54.2 && $lng <= 54.6) {
            return City::whereHas('translations', function ($q) {
                $q->where('name', 'Abu Dhabi');
            })->where('status', 'active')->first();
        }

        // Dubai: ~25.2048, 55.2708
        if ($lat >= 25.0 && $lat <= 25.4 && $lng >= 55.0 && $lng <= 55.5) {
            return City::whereHas('translations', function ($q) {
                $q->where('name', 'Dubai');
            })->where('status', 'active')->first();
        }

        // Sharjah: ~25.3463, 55.4209
        if ($lat >= 25.2 && $lat <= 25.4 && $lng >= 55.3 && $lng <= 55.6) {
            return City::whereHas('translations', function ($q) {
                $q->where('name', 'Sharjah');
            })->where('status', 'active')->first();
        }

        // Ajman: ~25.4111, 55.4354
        if ($lat >= 25.35 && $lat <= 25.45 && $lng >= 55.4 && $lng <= 55.5) {
            return City::whereHas('translations', function ($q) {
                $q->where('name', 'Ajman');
            })->where('status', 'active')->first();
        }

        // Umm Al Quwain: ~25.5647, 55.5552
        if ($lat >= 25.5 && $lat <= 25.6 && $lng >= 55.5 && $lng <= 55.7) {
            return City::whereHas('translations', function ($q) {
                $q->where('name', 'Umm Al Quwain');
            })->where('status', 'active')->first();
        }

        // Ras Al Khaimah: ~25.7889, 55.9414
        if ($lat >= 25.6 && $lat <= 25.9 && $lng >= 55.8 && $lng <= 56.1) {
            return City::whereHas('translations', function ($q) {
                $q->where('name', 'Ras Al Khaimah');
            })->where('status', 'active')->first();
        }

        // Fujairah: ~25.1164, 56.3257
        if ($lat >= 24.9 && $lat <= 25.3 && $lng >= 56.2 && $lng <= 56.5) {
            return City::whereHas('translations', function ($q) {
                $q->where('name', 'Fujairah');
            })->where('status', 'active')->first();
        }

        // If no specific city found, return null
        return null;
    }

    protected function findAreaInCity(City $city, Point $point): ?\App\Models\Area
    {
        try {
            // Try spatial query first
            return $city->areas()
                ->where('status', 'active')
                ->whereContains('area', $point)
                ->first();
        } catch (\Exception $e) {
            // If spatial query fails, just return the first active area
            return $city->areas()
                ->where('status', 'active')
                ->first();
        }
    }

    protected function validateShippingDistance(array $pickupDetails, array $dropoffDetails, int $itemNumber): string|bool
    {
        switch ($this->shippingTypeId) {
            case 1: // Immediate shipping
                return $this->validateImmediateShipping($pickupDetails, $dropoffDetails, $itemNumber);

            case 2: // Intercity shipping
                return $this->validateIntercityShipping($pickupDetails, $dropoffDetails, $itemNumber);

            case 3: // International shipping
                return $this->validateInternationalShipping($pickupDetails, $dropoffDetails, $itemNumber);

            default:
                return "Invalid shipping type.";
        }
    }

    protected function validateImmediateShipping(array $pickupDetails, array $dropoffDetails, int $itemNumber): string|bool
    {
        // Must be in the same city
        if ($pickupDetails['city_id'] !== $dropoffDetails['city_id']) {
            return "Item {$itemNumber}: Immediate shipping requires pickup and dropoff to be within the same city.";
        }

        // Must be in different areas
        if (
            $pickupDetails['area_id'] && $dropoffDetails['area_id'] &&
            $pickupDetails['area_id'] === $dropoffDetails['area_id']
        ) {
            return "Item {$itemNumber}: Immediate shipping requires pickup and dropoff to be in different areas within the same city.";
        }

        return true;
    }

    protected function validateIntercityShipping(array $pickupDetails, array $dropoffDetails, int $itemNumber): string|bool
    {
        // Must be in different cities
        if ($pickupDetails['city_id'] === $dropoffDetails['city_id']) {
            return "Item {$itemNumber}: Intercity shipping requires pickup and dropoff to be in different cities.";
        }

        // Must be in the same country
        if ($pickupDetails['country_code'] !== $dropoffDetails['country_code']) {
            return "Item {$itemNumber}: Intercity shipping requires pickup and dropoff to be within the same country.";
        }

        return true;
    }

    protected function validateInternationalShipping(array $pickupDetails, array $dropoffDetails, int $itemNumber): string|bool
    {
        // Must be in different countries
        if ($pickupDetails['country_code'] === $dropoffDetails['country_code']) {
            return "Item {$itemNumber}: International shipping requires pickup and dropoff to be in different countries.";
        }

        return true;
    }
}
