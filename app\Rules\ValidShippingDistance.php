<?php

namespace App\Rules;

use App\Models\Address;
use App\Repositories\CityRepository;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use MatanYadaev\EloquentSpatial\Objects\Point;

class ValidShippingDistance implements ValidationRule
{
    protected $shippingTypeId;
    protected $pickupAddressId;
    protected $pickupLocation;
    protected $items;

    public function __construct($shippingTypeId, $pickupAddressId, $pickupLocation, $items)
    {
        $this->shippingTypeId = $shippingTypeId;
        $this->pickupAddressId = $pickupAddressId;
        $this->pickupLocation = $pickupLocation;
        $this->items = $items;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Get pickup location details
        $pickupDetails = $this->getLocationDetails($this->pickupAddressId, $this->pickupLocation);

        if (!$pickupDetails) {
            $fail('Unable to determine pickup location details.');
            return;
        }

        // Validate each item's dropoff location
        foreach ($this->items as $index => $item) {
            $dropoffDetails = $this->getLocationDetails(
                $item['dropoff_address_id'] ?? null,
                $item['dropoff_location'] ?? null
            );

            if (!$dropoffDetails) {
                $fail("Unable to determine dropoff location details for item " . ($index + 1) . ".");
                continue;
            }

            $validationResult = $this->validateShippingDistance($pickupDetails, $dropoffDetails, $index + 1);

            if ($validationResult !== true) {
                $fail($validationResult);
            }
        }
    }

    protected function getLocationDetails($addressId, $location)
    {
        if ($addressId) {
            // Get details from address
            $address = Address::with(['city', 'area'])->find($addressId);

            dd($address);

            if (!$address) {
                return null;
            }

            return [
                'country_code' => $address->country_code,
                'city_id' => $address->city_id,
                'area_id' => $address->area_id,
                'coordinates' => $address->location
            ];
        } elseif ($location && isset($location['lat'], $location['lng'])) {
            // Get details from coordinates
            $point = new Point($location['lat'], $location['lng']);

            dd($point);

            $cityRepository = app(CityRepository::class);
            $city = $cityRepository->getByLocation($location['lat'], $location['lng']);

            if (!$city) {
                return null;
            }

            // Find the area within the city that contains this point
            $area = $city->areas()
                ->where('status', 'active')
                ->whereContains('area', $point)
                ->first();

            // For UAE cities, we'll assume country code is 'AE' since all cities are in UAE
            return [
                'country_code' => 'AE', // Default to UAE for coordinate-based locations
                'city_id' => $city->id,
                'area_id' => $area->id ?? null,
                'coordinates' => $point
            ];
        }

        return null;
    }

    protected function validateShippingDistance($pickupDetails, $dropoffDetails, $itemNumber)
    {
        switch ($this->shippingTypeId) {
            case 1: // Immediate shipping
                return $this->validateImmediateShipping($pickupDetails, $dropoffDetails, $itemNumber);

            case 2: // Intercity shipping
                return $this->validateIntercityShipping($pickupDetails, $dropoffDetails, $itemNumber);

            case 3: // International shipping
                return $this->validateInternationalShipping($pickupDetails, $dropoffDetails, $itemNumber);

            default:
                return "Invalid shipping type.";
        }
    }

    protected function validateImmediateShipping($pickupDetails, $dropoffDetails, $itemNumber)
    {
        // For immediate shipping, pickup and dropoff must be in different areas
        if ($pickupDetails['area_id'] && $dropoffDetails['area_id']) {
            if ($pickupDetails['area_id'] === $dropoffDetails['area_id']) {
                return "Item {$itemNumber}: Immediate shipping requires pickup and dropoff to be in different areas within the same city.";
            }
        }

        // They must be in the same city for immediate shipping
        if ($pickupDetails['city_id'] !== $dropoffDetails['city_id']) {
            return "Item {$itemNumber}: Immediate shipping requires pickup and dropoff to be within the same city.";
        }

        return true;
    }

    protected function validateIntercityShipping($pickupDetails, $dropoffDetails, $itemNumber)
    {
        // For intercity shipping, pickup and dropoff must be in different cities
        if ($pickupDetails['city_id'] === $dropoffDetails['city_id']) {
            return "Item {$itemNumber}: Intercity shipping requires pickup and dropoff to be in different cities.";
        }

        // They should be in the same country for intercity shipping
        if ($pickupDetails['country_code'] !== $dropoffDetails['country_code']) {
            return "Item {$itemNumber}: Intercity shipping requires pickup and dropoff to be within the same country.";
        }

        return true;
    }

    protected function validateInternationalShipping($pickupDetails, $dropoffDetails, $itemNumber)
    {
        // For international shipping, pickup and dropoff must be in different countries
        if ($pickupDetails['country_code'] === $dropoffDetails['country_code']) {
            return "Item {$itemNumber}: International shipping requires pickup and dropoff to be in different countries.";
        }

        return true;
    }
}
