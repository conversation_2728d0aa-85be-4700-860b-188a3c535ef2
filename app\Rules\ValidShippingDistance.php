<?php

namespace App\Rules;

use Closure;
use App\Models\Address;
use App\Models\City;
use App\Repositories\CityRepository;
use MatanYadaev\EloquentSpatial\Objects\Point;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidShippingDistance implements ValidationRule
{
    protected int $shippingTypeId;
    protected ?int $pickupAddressId;
    protected ?array $pickupLocation;
    protected array $items;

    public function __construct($shippingTypeId, $pickupAddressId, $pickupLocation, $items)
    {
        $this->shippingTypeId = $shippingTypeId;
        $this->pickupAddressId = $pickupAddressId;
        $this->pickupLocation = $pickupLocation;
        $this->items = $items;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $pickupDetails = $this->resolveLocationDetails($this->pickupAddressId, $this->pickupLocation);

        if (!$pickupDetails) {
            $fail('Unable to determine pickup location details.');
            return;
        }

        foreach ($this->items as $index => $item) {
            $dropoffDetails = $this->resolveLocationDetails(
                $item['dropoff_address_id'] ?? null,
                $item['dropoff_location'] ?? null
            );

            if (!$dropoffDetails) {
                $fail("Item #" . ($index + 1) . ": Unable to determine dropoff location details.");
                continue;
            }

            $error = $this->validateShippingMatch($pickupDetails, $dropoffDetails, $index + 1);
            if ($error !== true) {
                $fail($error);
            }
        }
    }

    protected function resolveLocationDetails($addressId = null, $location = null): ?array
    {
        if ($addressId) {
            $address = Address::with(['city', 'area'])->find($addressId);
            if (!$address) return null;

            return [
                'country_code' => $address->country_code,
                'city_id'      => $address->city_id,
                'area_id'      => $address->area_id,
                'coordinates'  => $address->location,
            ];
        }

        if ($location && isset($location['lat'], $location['lng'])) {
            $lat = $location['lat'];
            $lng = $location['lng'];
            $point = new Point($lat, $lng);

            $city = app(CityRepository::class)->getByLocation($lat, $lng);

            if (!$city) {
                $city = City::whereRaw('ST_Contains(area, ST_GeomFromText(?, 4326))', [
                    "POINT($lng $lat)"
                ])->where('status', 'active')->first();
            }

            if (!$city) return null;

            $area = $city->areas()
                ->where('status', 'active')
                ->whereContains('area', $point)
                ->first();

            return [
                'country_code' => 'AE',
                'city_id'      => $city->id,
                'area_id'      => $area?->id,
                'coordinates'  => $point,
            ];
        }

        return null;
    }

    protected function validateShippingMatch(array $pickup, array $dropoff, int $itemNumber): true|string
    {
        return match ($this->shippingTypeId) {
            1 => $this->validateImmediate($pickup, $dropoff, $itemNumber),
            2 => $this->validateIntercity($pickup, $dropoff, $itemNumber),
            3 => $this->validateInternational($pickup, $dropoff, $itemNumber),
            default => "Invalid shipping type for item #$itemNumber.",
        };
    }

    protected function validateImmediate(array $pickup, array $dropoff, int $item): true|string
    {
        if ($pickup['city_id'] !== $dropoff['city_id']) {
            return "Item #$item: Immediate shipping requires pickup and dropoff to be in the same city.";
        }

        if ($pickup['area_id'] === $dropoff['area_id']) {
            return "Item #$item: Immediate shipping requires pickup and dropoff to be in different areas.";
        }

        return true;
    }

    protected function validateIntercity(array $pickup, array $dropoff, int $item): true|string
    {
        if ($pickup['city_id'] === $dropoff['city_id']) {
            return "Item #$item: Intercity shipping requires pickup and dropoff to be in different cities.";
        }

        if ($pickup['country_code'] !== $dropoff['country_code']) {
            return "Item #$item: Intercity shipping must occur within the same country.";
        }

        return true;
    }

    protected function validateInternational(array $pickup, array $dropoff, int $item): true|string
    {
        if ($pickup['country_code'] === $dropoff['country_code']) {
            return "Item #$item: International shipping requires pickup and dropoff to be in different countries.";
        }

        return true;
    }
}
