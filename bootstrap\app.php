<?php

use App\Http\Middleware\CheckAdminStatus;
use App\Http\Middleware\SetLocale;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Illuminate\Support\Facades\Route;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        commands: __DIR__ . '/../routes/console.php',
        using: function () {
            Route::middleware('web')
                ->group(base_path('routes/web.php'));

            Route::middleware(['api', 'setLocale'])
                ->prefix('api')
                ->group(base_path('routes/api.php'));

            Route::middleware('web')
                ->prefix('admin-dashboard')
                ->name('admin.')
                ->group(base_path('routes/admin.php'));

            Route::middleware('web')
                ->prefix('company-dashboard')
                ->name('company.')
                ->group(base_path('routes/company.php'));

            Route::middleware(['api', 'setLocale'])
                ->prefix('api/user')
                ->name('user.')
                ->group(base_path('routes/user.php'));

            Route::middleware(['api', 'setLocale'])
                ->prefix('api/driver')
                ->name('driver.')
                ->group(base_path('routes/driver.php'));
        }
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'checkAdminStatus' => CheckAdminStatus::class,
            'setLocale' => SetLocale::class
        ]);

        $middleware->validateCsrfTokens(except: [
            'company-dashboard/register',
            'company-dashboard/registration-stepper-validation',
            'company-dashboard/send-verification-code',
            'company-dashboard/verify-email',
            'company-dashboard/allowed-to-send-verification-code',
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->renderable(function (NotFoundHttpException $e) {
            return errorMessage($e->getMessage() ? $e->getMessage() : __("resource not found"), 404);
        });

        $exceptions->renderable(function (BadRequestHttpException $e) {
            return errorMessage($e->getMessage());
        });

        $exceptions->renderable(function (ThrottleRequestsException $e) {
            return errorMessage(__('Too Many Attempts.'), 429);
        });
    })->create();
