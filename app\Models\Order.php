<?php

namespace App\Models;

use App\Enum\OrderPaymentMethod;
use App\Enum\OrderStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use MatanYadaev\EloquentSpatial\Objects\Point;
use MatanYadaev\EloquentSpatial\Traits\HasSpatial;

class Order extends Model
{
    use HasSpatial, SoftDeletes;

    protected $fillable = [
        'user_id',
        'driver_id',
        'shipping_type_id',
        'shipping_size_id',
        'pickup_address_id',
        'pickup_location',
        'description',
        'payment_method',
        'status',
        'is_express',
    ];

    protected $casts = [
        'pickup_location' => Point::class,
        'payment_method' => OrderPaymentMethod::class,
        'status' => OrderStatus::class,
        'is_express' => 'boolean'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function driver()
    {
        return $this->belongsTo(Driver::class);
    }

    public function shippingType()
    {
        return $this->belongsTo(ShippingType::class);
    }

    public function shippingSize()
    {
        return $this->belongsTo(ShippingSize::class);
    }

    public function pickupAddress()
    {
        return $this->belongsTo(Address::class, 'pickup_address_id');
    }

    public function items()
    {
        return $this->hasMany(OrderItem::class);
    }
}
