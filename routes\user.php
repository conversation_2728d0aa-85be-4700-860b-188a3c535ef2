<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\User\OrderController;
use App\Http\Controllers\User\RegistrationController;
use App\Http\Controllers\User\ShippingTypeController;
use App\Http\Controllers\User\AuthenticationController;

// Authentication
Route::group([
    'prefix' => 'auth',
], function () {
    Route::post('request-otp', [AuthenticationController::class, 'requestOTP']);
    Route::post('allowed-to-send-otp', [AuthenticationController::class, 'allowedToSendOTP']);
    Route::post('confirm-otp', [AuthenticationController::class, 'confirmCode']);
    Route::post('register', [RegistrationController::class, 'store']);
    Route::post('logout', [AuthenticationController::class, 'logout'])->middleware('auth:user');
});

Route::group([
    'middleware' => ['auth:user'],
], function () {
    Route::post('shipping-types/by-location', [ShippingTypeController::class, 'byLocation']);
    Route::apiResource('orders', OrderController::class);
});
