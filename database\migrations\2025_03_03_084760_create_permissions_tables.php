<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('roles', function (Blueprint $table) {
            $table->id();
            $table->string('type');
            $table->string('name');
            $table->boolean('status')->default(1);
            $table->timestamps();
            $table->unique(['type', 'name']);
        });

        Schema::create('permissions', function (Blueprint $table) {
            $table->id();
            $table->string('type');
            $table->string('name');
            $table->string('group');
            $table->unique(['type', 'name', 'group']);
        });

        Schema::disableForeignKeyConstraints();

        Schema::create('role_permission', function (Blueprint $table) {
            $table->id();
            $table->foreignId('role_id')->constrained()->cascadeOnDelete();
            $table->foreignId('permission_id')->constrained()->cascadeOnDelete();
            $table->unique(['role_id', 'permission_id']);
        });

        Schema::create('model_permission', function (Blueprint $table) {
            $table->id();
            $table->morphs('model');
            $table->foreignId('permission_id')->constrained()->cascadeOnDelete();
            $table->unique(['model_type', 'model_id', 'permission_id']);
        });

       Schema::enableForeignKeyConstraints();
    }

    public function down(): void
    {
        Schema::dropIfExists('roles');
        Schema::dropIfExists('permissions');
        Schema::dropIfExists('role_permission');
        Schema::dropIfExists('model_permission');
    }
};
