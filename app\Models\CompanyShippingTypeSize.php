<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CompanyShippingTypeSize extends Model
{
    public $timestamps = false;

    protected $fillable = [
        'company_shipping_type_id',
        'shipping_size_id'
    ];


    public function transportionMethods()
    {
        return $this->hasMany(CompanyShippingTypeSizeTransportionMethod::class);
    }

    public function size()
    {
        return $this->belongsTo(ShippingSize::class, 'shipping_size_id');
    }
}
