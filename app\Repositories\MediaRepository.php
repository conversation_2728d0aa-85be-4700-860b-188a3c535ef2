<?php

namespace App\Repositories;

use App\Models\Media;
use Illuminate\Database\Eloquent\Model;

class MediaRepository
{
    public function __construct(private Media $model) {}

    public function save(Model $record, $file, string $path, ?string $type = null)
    {
        return $this->model->create([
            'mediable_type' => $record::class,
            'mediable_id' => $record->id,
            'filename' => $file->getClientOriginalName(),
            'path' => $path,
            'extension' => $file->getClientOriginalExtension(),
            'mime' => $file->getClientMimeType(),
            'size' => ceil($file->getSize() / 1024) . ' kb',
            'type' => $type
        ]);
    }
}
