<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class UniqueShippingOptionCities implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!is_array($value)) {
            return;
        }

        $cityIds = array_column($value, 'id');

        $seen = [];
        $duplicates = [];

        foreach ($cityIds as $cityId) {
            if (isset($seen[$cityId])) {
                $duplicates[] = $cityId;
            } else {
                $seen[$cityId] = true;
            }
        }

        if (!empty($duplicates)) {
            $fail("Duplicate cities found in {$attribute}.");
        }
    }
}
