<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use MatanYadaev\EloquentSpatial\Objects\Point;
use MatanYadaev\EloquentSpatial\Traits\HasSpatial;

class OrderItem extends Model
{
    use HasSpatial;

    protected $fillable = [
        'order_id',
        'shipment_number',
        'cost',
        'fees',
        'dropoff_address_id',
        'dropoff_location',
        'recipient_name',
        'country_code',
        'phone',
        'description',
    ];

    protected $casts = [
        'dropoff_location' => Point::class,
        'cost' => 'decimal:2',
        'fees' => 'decimal:2',
    ];

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function dropoffAddress()
    {
        return $this->belongsTo(Address::class, 'dropoff_address_id');
    }
}
