<?php

namespace App\DTO;

use App\Enum\OrderStatus;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Optional;

class OrderDTO extends Data
{
    public function __construct(
        public int $user_id,
        public int $shipping_type_id,
        public int $shipping_size_id,
        public int $pickup_address_id,
        public string $payment_method,
        public string|Optional $status = new Optional(OrderStatus::READY_TO_PICKUP->value),
        public bool $is_express,
    ) {
        //
    }
}
