<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseFormRequest;
use App\Models\User;
use App\Rules\UniquePhone;
use App\Rules\ValidBirthDate;
use App\Rules\ValidMedia;
use App\Rules\ValidPhone;
use App\Services\MediaService;

class CompleteProfileRequest extends BaseFormRequest
{
    public function __construct(private MediaService $mediaService) {}

    public function rules(): array
    {
        return [
            'country_code'  => ['required', 'string', 'exists:countries,code'],
            'phone' => ['required', 'string', new ValidPhone($this->country_code), new UniquePhone($this->country_code, User::class)],
            'code' => ['required', 'string'],
            'name' => ['required', 'string', 'min:4', 'max:30'],
            'email' => ['nullable', 'string', 'email:rfc,dns', 'max:255', 'unique:users,email'],
            'gender' => ['nullable', 'in:male,female'],
            'birth_date' => ['nullable', new ValidBirthDate],
            'profile_image' => ['nullable', new ValidMedia(['image'])],
        ];
    }

    public function messages(): array
    {
        return [
            'birth_date.before_or_equal' => __("You must be at least 18 years old to register"),
        ];
    }
}
