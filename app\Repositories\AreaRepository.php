<?php

namespace App\Repositories;

use App\Models\Area;
use App\Models\City;

class AreaRepository
{
    public function __construct(private Area $model) {}

    public function getById(string $id)
    {
        return $this->model->find($id);
    }

    public function getByIds(array $ids)
    {
        return $this->model->whereIn('id', $ids)->get();
    }

    public function getByCity(City $city)
    {
        return $this->model->where('city_id', $city->id)->get();
    }
}
