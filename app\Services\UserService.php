<?php

namespace App\Services;

use App\Repositories\UserRepository;

class UserService
{
    public function __construct(private UserRepository $userRepository, private MediaService $mediaService) {}

    public function register(array $data)
    {
        $user = $this->userRepository->register($data);

        if (request('profile_image')) {
            $this->mediaService->save($user, request('profile_image'), 'users');
        }

        return $user;
    }
}
