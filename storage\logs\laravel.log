[2025-07-15 13:00:54] local.INFO: Starting shipping distance validation {"shipping_type_id":"1","items_count":1} 
[2025-07-15 13:00:54] local.INFO: ValidShippingDistance rule triggered {"shipping_type_id":"1","pickup_address_id":null,"pickup_location":{"lat":"24.7","lng":"54.2"},"items_count":1} 
[2025-07-15 13:00:54] local.INFO: Point created from coordinates {"lat":"24.7","lng":"54.2","point":{"MatanYadaev\\EloquentSpatial\\Objects\\Point":{"type":"Point","coordinates":[54.2,24.7]}}} 
[2025-07-15 13:00:54] local.ERROR: Unable to determine pickup location details  
[2025-07-15 13:00:54] local.ERROR: Shipping distance validation failed {"message":"Unable to determine pickup location details."} 
[2025-07-15 13:05:36] local.INFO: Starting shipping distance validation {"shipping_type_id":"1","items_count":1} 
[2025-07-15 13:05:36] local.INFO: ValidShippingDistance rule triggered {"shipping_type_id":"1","pickup_address_id":null,"pickup_location":{"lat":"54.2","lng":"24.7"},"items_count":1} 
[2025-07-15 13:05:36] local.INFO: Point created from coordinates {"lat":"54.2","lng":"24.7","point":{"MatanYadaev\\EloquentSpatial\\Objects\\Point":{"type":"Point","coordinates":[24.7,54.2]}}} 
[2025-07-15 13:05:36] local.ERROR: Unable to determine pickup location details  
[2025-07-15 13:05:36] local.ERROR: Shipping distance validation failed {"message":"Unable to determine pickup location details."} 
[2025-07-15 13:05:46] local.INFO: Starting shipping distance validation {"shipping_type_id":"1","items_count":1} 
[2025-07-15 13:05:46] local.INFO: ValidShippingDistance rule triggered {"shipping_type_id":"1","pickup_address_id":null,"pickup_location":{"lat":"54.2","lng":"24.7"},"items_count":1} 
[2025-07-15 13:05:46] local.INFO: Point created from coordinates {"lat":"54.2","lng":"24.7","point":{"MatanYadaev\\EloquentSpatial\\Objects\\Point":{"type":"Point","coordinates":[24.7,54.2]}}} 
[2025-07-15 13:05:46] local.ERROR: Unable to determine pickup location details  
[2025-07-15 13:05:46] local.ERROR: Shipping distance validation failed {"message":"Unable to determine pickup location details."} 
[2025-07-15 13:06:33] local.INFO: Starting shipping distance validation {"shipping_type_id":"1","items_count":1} 
[2025-07-15 13:06:33] local.INFO: ValidShippingDistance rule triggered {"shipping_type_id":"1","pickup_address_id":null,"pickup_location":{"lat":"54.2","lng":"24.7"},"items_count":1} 
[2025-07-15 13:06:33] local.INFO: Point created from coordinates {"lat":"54.2","lng":"24.7","point":{"MatanYadaev\\EloquentSpatial\\Objects\\Point":{"type":"Point","coordinates":[24.7,54.2]}}} 
[2025-07-15 13:07:01] local.INFO: Starting shipping distance validation {"shipping_type_id":"1","items_count":1} 
[2025-07-15 13:07:01] local.INFO: ValidShippingDistance rule triggered {"shipping_type_id":"1","pickup_address_id":null,"pickup_location":{"lat":"54.2","lng":"24.2"},"items_count":1} 
[2025-07-15 13:07:01] local.INFO: Point created from coordinates {"lat":"54.2","lng":"24.2","point":{"MatanYadaev\\EloquentSpatial\\Objects\\Point":{"type":"Point","coordinates":[24.2,54.2]}}} 
