<?php

namespace App\Models;

use App\Traits\HasPermissions;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Admin extends Model
{
    use HasPermissions, SoftDeletes;

    protected $fillable = [
        'name',
        'country_id',
        'country_code',
        'phone',
        'email',
        'role_id',
        'status',
    ];
    

     public function getPhoneAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function getEmailAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }
}
