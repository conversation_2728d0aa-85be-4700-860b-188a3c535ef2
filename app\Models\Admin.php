<?php

namespace App\Models;

use App\Traits\HasPermissions;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\SoftDeletes;

class Admin extends Authenticatable
{
    use HasPermissions, SoftDeletes;

    protected $fillable = [
        'name',
        'country_code',
        'phone',
        'email',
        'password',
        'role_id',
        'status',
        'remember_token',
        'email_verified_at',
    ];


    public function getPhoneAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function getEmailAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function image()
    {
        return $this->morphOne(Media::class, 'mediable');
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    public function getImageUrlAttribute()
    {
        return $this->image ? $this->image->url : asset('user-default.png');
    }
}
