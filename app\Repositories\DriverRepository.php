<?php

namespace App\Repositories;

use App\Models\Driver;

class DriverRepository
{
    public function __construct(private Driver $model) {}

    public function getByPhone($country_code, $phone)
    {
        return $this->model->where('country_code', $country_code)->where('phone', $phone)->first();
    }

    public function register(array $data)
    {
        return $this->model->create([
            'name' => $data['name'],
            'country_code' => $data['country_code'],
            'phone' => $data['phone'],
            'email' => $data['email'] ?? null,
            'company_id' => $data['company_id'],
            'gender' => $data['gender'] ?? null,
            'birth_date' => $data['birth_date'] ?? null,
            'id_number' => $data['id_number'] ?? null,
            'bank_name' => $data['bank_name'],
            'bank_account_owner' => $data['bank_account_owner'],
            'bank_account_number' => $data['bank_account_number'],
            'iban' => $data['iban'] ?? null,
        ]);
    }
}
