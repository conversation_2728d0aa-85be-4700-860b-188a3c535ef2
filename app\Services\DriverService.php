<?php

namespace App\Services;

use App\Repositories\CompanyImmediateShippingCityRepository;
use App\Repositories\CompanyRepository;
use App\Repositories\CompanyShippingTypeRepository;
use App\Repositories\CompanyShippingTypeSizeTransportionMethodRepository;
use App\Repositories\DriverImmediateShippingCityAreaRepository;
use App\Repositories\DriverImmediateShippingCityRepository;
use App\Repositories\DriverRepository;
use App\Repositories\DriverShippingTypeRepository;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class DriverService
{
    public function __construct(
        private DriverRepository $driverRepository,
        private MediaService $mediaService,
        private CompanyRepository $companyRepository,
        private DriverShippingTypeRepository $driverShippingTypeRepository,
        private DriverImmediateShippingCityRepository $driverImmediateShippingCityRepository,
        private DriverImmediateShippingCityAreaRepository $driverImmediateShippingCityAreaRepository,
        private CompanyShippingTypeRepository $companyShippingTypeRepository,
        private CompanyShippingTypeSizeTransportionMethodRepository $companyShippingTypeSizeTransportionMethodRepository,
        private CompanyImmediateShippingCityRepository $companyImmediateShippingCityRepository
    ) {}

    public function register(array $data)
    {
        $this->validateRegistration();

        $data['company_id'] = $this->companyRepository->getByCode(request('company_code'))->id;

        $driver = DB::transaction(function () use ($data) {

            $driver = $this->driverRepository->register($data);

            if (request('profile_image')) {
                $this->mediaService->save($driver, request('profile_image'), 'drivers', 'profile');
            }

            if (request('id_number_image')) {
                $this->mediaService->save($driver, request('id_number_image'), 'drivers', 'id_number');
            }

            if (request('driving_licence_image')) {
                $this->mediaService->save($driver, request('driving_licence_image'), 'drivers', 'driving_licence');
            }

            if (request('additional_attachments')) {
                foreach (request('additional_attachments') as $attachment) {
                    $this->mediaService->save($driver, $attachment, 'drivers', 'additional');
                }
            }

            foreach (request('shipping_options') as $shipping_option) {
                $driverShippingType = $this->driverShippingTypeRepository->create($driver, $shipping_option['shipping_type_id']);
                $driverShippingType->transportionMethods()->attach($shipping_option['transportion_methods']);

                // Immediate shipping
                if ($shipping_option['shipping_type_id'] == 1) {
                    foreach ($shipping_option['cities'] as $city) {
                        $driverImmediateShippingCity = $this->driverImmediateShippingCityRepository->create($driver, $city['id']);

                        foreach ($city['areas'] as $area_id) {
                            $this->driverImmediateShippingCityAreaRepository->create($driverImmediateShippingCity, $area_id);
                        }
                    }
                }

                // Intercity shipping
                if ($shipping_option['shipping_type_id'] == 2) {
                    $cityIds = array_column($shipping_option['cities'], 'id');
                    $driver->intercityShippingCities()->attach($cityIds, ['shipping_type' => 'intercity']);
                }

                // International shipping
                if ($shipping_option['shipping_type_id'] == 3) {
                    $cityIds = array_column($shipping_option['cities'], 'id');
                    $driver->internationalShippingCities()->attach($cityIds, ['shipping_type' => 'international']);
                }
            }

            return $driver;
        });

        return $driver;
    }

    public function validateRegistration()
    {
        $company = $this->companyRepository->getByCode(request('company_code'));

        foreach (request('shipping_options') as $shipping_option) {
            $companyShippingType = $this->companyShippingTypeRepository->get($company, $shipping_option['shipping_type_id']);

            // Check if company has this shipping type
            if (!$companyShippingType) {
                throw new BadRequestHttpException("company doesn't have shipping type id {$shipping_option['shipping_type_id']}");
            }

            // Check if company has these transportion methods
            foreach ($shipping_option['transportion_methods'] as $transportion_method) {
                $companyShippingTypeHasTransportionMethod = $this->companyShippingTypeSizeTransportionMethodRepository->shippingTypeHasTransportionMethod($companyShippingType, $transportion_method);

                if (!$companyShippingTypeHasTransportionMethod) {
                    throw new BadRequestHttpException("company shipping type id {$companyShippingType->shipping_type_id} doesn't have transportion method id $transportion_method");
                }
            }


            // Check if company has these cities
            foreach ($shipping_option['cities'] as $city) {

                // Immediate shipping
                if ($shipping_option['shipping_type_id'] == 1) {

                    $companyShippingCity = $this->companyImmediateShippingCityRepository->get($company, $city['id']);

                    if (!$companyShippingCity) {
                        throw new BadRequestHttpException("company doesn't have city id {$city['id']} for immediate shipping");
                    }

                    // check if city has area
                    foreach ($city['areas'] as $area_id) {

                        $cityHasArea = $this->companyImmediateShippingCityRepository->cityHasArea($companyShippingCity, $area_id);

                        if (!$cityHasArea) {
                            throw new BadRequestHttpException("company city id {$city['id']} doesn't have area id $area_id");
                        }
                    }
                }

                // Intercity shipping
                if ($shipping_option['shipping_type_id'] == 2) {

                    $companyHasPickupCity = $this->companyRepository->hasIntercityShippingPickupCity($company, $city['id']);

                    if (!$companyHasPickupCity) {
                        throw new BadRequestHttpException("company doesn't have city id {$city['id']} for intercity shipping");
                    }
                }

                // International shipping
                if ($shipping_option['shipping_type_id'] == 3) {

                    $companyHasPickupCity = $this->companyRepository->hasInternationalShippingCity($company, $city['id']);

                    if (!$companyHasPickupCity) {
                        throw new BadRequestHttpException("company doesn't have city id {$city['id']} for international shipping");
                    }
                }
            }
        }
    }
}
