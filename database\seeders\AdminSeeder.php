<?php

namespace Database\Seeders;

use App\Models\Admin;
use App\Models\Country;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AdminSeeder extends Seeder
{
    public function run(): void
    {
        for ($i = 1; $i <= 3; $i++) {
            $country = Country::find(fake()->randomElement([1, 2]));

            $admin = Admin::create([
                'name' => "Admin $i",
                'country_code' => $country->code,
                'phone' => fake()->numerify('##########'),
                'email' => "admin$<EMAIL>",
                'password' => bcrypt('Packz4000&'),
                'role_id' => 1,
            ]);

            $admin->permissions()->attach($admin->role->permissions);
        }
    }
}
