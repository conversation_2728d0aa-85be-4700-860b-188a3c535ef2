<?php

namespace App\Http\Controllers\Admin\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\LoginRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;

class AuthenticationController extends Controller
{
    public function loginView()
    {
        return view('auth.login');
    }

    public function login(LoginRequest $request)
    {
        if (RateLimiter::tooManyAttempts("login-attempts-admin:$request->email", 3)) {

            $seconds = RateLimiter::availableIn("login-attempts-admin:$request->email");
            $remainingTime = formatSecondsToMinutesTime($seconds);

            return back()->with('fail', __("too many attempts: retry after :time minutes", ['time' => $remainingTime]));
        }

        RateLimiter::hit("login-attempts-admin:$request->email", 120);

        $credentials = ['email' => $request->email, 'password' => $request->password];

        if (!auth('admin')->attempt($credentials, $request->boolean('remember'))) {
            throw ValidationException::withMessages([
                'email' => __('auth.failed')
            ]);
        }

        if (auth('admin')->user()->status != 'active') {
            auth('admin')->logout();
            $request->session()->invalidate();

            return back()->with('fail', __("your account is disabled"));
        }

        $request->session()->regenerate();

        return redirect()->intended(route('admin.index'));
    }

    public function logout(Request $request)
    {
        auth('admin')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return to_route('admin.login');
    }
}
