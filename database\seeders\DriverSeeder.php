<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\Driver;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DriverSeeder extends Seeder
{
    public function run(): void
    {
        for ($i = 1; $i <= 5; $i++) {
            $company = Company::inRandomOrder()->first();

            $driver = Driver::create([
                'name' => "Driver $i",
                'country_id' => fake()->randomElement([1, 2]),
                'country_code' => '+20',
                'phone' => "**********$i",
                'email' => "driver$<EMAIL>",
                'birth_date' => fake()->optional()->date(),
                'gender' => fake()->optional()->randomElement(['male', 'female']),
                'company_id' => $company->id,
                'national_id' => fake()->randomNumber(4, true) . fake()->randomNumber(6, true),
                'bank' => fake()->company(),
                'bank_account_owner' => "Driver $i",
                'bank_account_number' => fake()->bankAccountNumber(),
                'iban' => fake()->optional()->iban('EG'),
            ]);
        }
    }
}
