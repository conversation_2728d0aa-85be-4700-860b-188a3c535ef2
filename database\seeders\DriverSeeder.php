<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\Driver;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DriverSeeder extends Seeder
{
    public function run(): void
    {
        for ($i = 1; $i <= 5; $i++) {
            $company = Company::inRandomOrder()->first();

            $driver = Driver::create([
                'name' => "Driver $i",
                'country_code' => '+971',
                'phone' => "********$i",
                'email' => "driver$<EMAIL>",
                'birth_date' => fake()->optional()->date(),
                'gender' => fake()->optional()->randomElement(['male', 'female']),
                'company_id' => $company->id,
                'id_number' => fake()->randomNumber(4, true) . fake()->randomNumber(6, true),
                'bank_name' => fake()->company(),
                'bank_account_owner' => "Driver $i",
                'bank_account_number' => fake()->bankAccountNumber(),
                'iban' => fake()->optional()->iban('EG'),
            ]);
        }
    }
}
