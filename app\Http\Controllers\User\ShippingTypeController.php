<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Services\User\ShippingTypeService;
use Illuminate\Http\Request;

class ShippingTypeController extends Controller
{
    public function __construct(private ShippingTypeService $shippingTypeService)
    {
        //
    }

    public function byLocation(Request $request)
    {
        $request->validate([
            'lat' => 'required|numeric',
            'lng' => 'required|numeric',
        ]);

        $shippingTypes = $this->shippingTypeService->getAvailableShippingTypesForLocation(
            $request->lat,
            $request->lng
        );

        return success($shippingTypes);
    }
}
