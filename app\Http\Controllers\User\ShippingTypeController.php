<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ShippingTypeController extends Controller
{
    public function __construct()
    {
        //
    }

    public function byLocation(Request $request)
    {
        $request->validate([
            'lat' => 'required|numeric',
            'lng' => 'required|numeric',
        ]);

        $service = new \App\Services\ShippingTypeService();

        return response()->json(
            $service->getAvailableShippingTypesForLocation($request->lat, $request->lng)
        );
    }
}
