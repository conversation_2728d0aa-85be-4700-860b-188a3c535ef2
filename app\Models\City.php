<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Astrotomic\Translatable\Translatable;
use MatanYadaev\EloquentSpatial\Traits\HasSpatial;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;

class City extends Model implements TranslatableContract
{
    use Translatable, HasSpatial;

    protected $fillable = [
        'status',
        'location',
        'area'
    ];

    public $translatedAttributes = [
        'name'
    ];

    public function casts()
    {
        return [
            'location' => 'point',
            'area' => 'polygon',
        ];
    }

    public function areas()
    {
        return $this->hasMany(Area::class);
    }

    public function drivers()
    {
        return $this->belongsToMany(Driver::class, 'driver_areas');
    }
}
