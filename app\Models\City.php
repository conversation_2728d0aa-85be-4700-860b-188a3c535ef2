<?php

namespace App\Models;

use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\Model;
use MatanYadaev\EloquentSpatial\Objects\Point;
use Matan<PERSON>adaev\EloquentSpatial\Objects\Polygon;
use MatanYadaev\EloquentSpatial\Traits\HasSpatial;

class City extends Model implements TranslatableContract
{
    use HasSpatial, Translatable;

    protected $fillable = [
        'status',
        'location',
        'area',
    ];

    public $translatedAttributes = [
        'name',
    ];

    protected $casts = [
        'location' => Point::class,
        'area' => Polygon::class,
    ];

    public function areas()
    {
        return $this->hasMany(Area::class);
    }

    public function drivers()
    {
        return $this->belongsToMany(Driver::class, 'driver_areas');
    }
}
