<?php

namespace App\Http\Resources;

use App\Http\Resources\CityResource;
use App\Http\Resources\TransportionMethodResource;
use App\Http\Resources\ImmediateShippingCityResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanyShippingTypeResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->shippingType->id,
            'name' => $this->shippingType->name,
            'transportion_methods' => TransportionMethodResource::collection($this->transportionMethods),
            'cities' => $this->getCitiesByShippingType()
        ];
    }

    private function getCitiesByShippingType()
    {
        return match ($this->shippingType->id) {
            1 => ImmediateShippingCityResource::collection($this->company->immediateShippingCities),
            2 => CityResource::collection($this->company->intercityShippingPickupCities),
            3 => CityResource::collection($this->company->internationalShippingCities),
            default => [],
        };
    }
}
