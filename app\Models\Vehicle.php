<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Vehicle extends Model
{
    protected $fillable = [
        'company_id',
        'company_shipping_type_size_transportion_method_id',
        'model',
        'year'
    ];


    public function photo()
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'photo');
    }

    public function registrationLicense()
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'registration_license');
    }
}
