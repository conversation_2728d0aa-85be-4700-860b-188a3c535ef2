<?php

namespace App\Services;

use App\Repositories\MediaRepository;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\UploadedFile;

class MediaService
{
    public function __construct(private MediaRepository $mediaRepository) {}

    public function save(Model $record, UploadedFile $file, string $directory, ?string $type = null)
    {
        $path = $file->store($directory);

        $fullPath = storage_path("app/public/$path");

        $mimeType = $file->getClientMimeType();

        if (str_starts_with($mimeType, 'image/')) {
            $this->compressImage($fullPath);
        }

        return $this->mediaRepository->save($record, $file, $path, $type);
    }

    public function getMaxUploadSize()
    {
        $maxUploadSize = ini_get('upload_max_filesize'); // in megabytes

        return preg_replace('/\D/', '', $maxUploadSize) * 1024; // get in kilobytes
    }

    public function compressImage(string $fullPath)
    {
        $supported_formats = \Imagick::queryFormats();
        $ext = strtoupper(pathinfo($fullPath, PATHINFO_EXTENSION));

        if (in_array($ext, $supported_formats)) {

            try {
                $image = new \Imagick($fullPath);

                $image->setImageCompressionQuality(20);

                file_put_contents($fullPath, $image);
            } catch (\Exception $e) {
            }
        }
    }
}
