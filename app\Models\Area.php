<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;
use MatanYadaev\EloquentSpatial\Traits\HasSpatial;

class Area extends Model implements TranslatableContract
{
    use Translatable, HasSpatial;

    protected $fillable = [
        'city_id',
        'status',
        'area'
    ];

    public $translatedAttributes = [
        'name'
    ];

    public function casts()
    {
        return [
            'area' => 'polygon',
        ];
    }

    public function city()
    {
        return $this->belongsTo(City::class);
    }

    public function drivers()
    {
        return $this->belongsToMany(Driver::class, 'driver_areas');
    }
}
