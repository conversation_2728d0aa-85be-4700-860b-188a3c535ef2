<?php

namespace App\Models;

use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\Model;
use MatanYadaev\EloquentSpatial\Objects\Polygon;
use MatanYadaev\EloquentSpatial\Traits\HasSpatial;

class Area extends Model implements TranslatableContract
{
    use HasSpatial, Translatable;

    protected $fillable = [
        'city_id',
        'status',
        'area',
    ];

    public $translatedAttributes = [
        'name',
    ];

    protected $casts = [
        'area' => Polygon::class,
    ];

    public function city()
    {
        return $this->belongsTo(City::class);
    }

    public function drivers()
    {
        return $this->belongsToMany(Driver::class, 'driver_areas');
    }
}
