<?php

namespace App\Repositories;

use App\Models\Company;

class CompanyRepository
{
    public function __construct(private Company $model) {}

    public function getById(string $id)
    {
        return $this->model->find($id);
    }

    public function getByCode(string $code)
    {
        return $this->model->where('code', $code)->first();
    }

    public function getByPhone($country_code, $phone)
    {
        return $this->model->where('country_code', $country_code)->where('phone', $phone)->first();
    }

    public function register(array $data)
    {
        return $this->model->create([
            'code' => $data['code'],
            'name' => $data['name'],
            'country_code' => $data['country_code'],
            'phone' => $data['phone'],
            'email' => $data['email'] ?? null,
            'address' => $data['address'] ?? null,
            'business_registration_number' => $data['business_registration_number'],
            'bank_name' => $data['bank_name'],
            'bank_account_owner' => $data['bank_account_owner'],
            'bank_account_number' => $data['bank_account_number'],
            'iban' => $data['iban'] ?? null
        ]);
    }

    public function hasIntercityShippingPickupCity(Company $company, string $city_id)
    {
        return $company->intercityShippingPickupCities()->where('city_id', $city_id)->exists();
    }

    public function hasIntercityShippingDeliveryCity(Company $company, string $city_id)
    {
        return $company->intercityShippingDeliveryCities()->where('city_id', $city_id)->exists();
    }

    public function hasInternationalShippingCity(Company $company, string $city_id)
    {
        return $company->internationalShippingCities()->where('city_id', $city_id)->exists();
    }
}
