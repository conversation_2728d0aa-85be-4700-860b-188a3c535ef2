<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\CompleteProfileRequest;
use App\Http\Resources\User\LoginResource;
use App\Models\User;
use App\Repositories\CountryRepository;
use App\Repositories\OTPRepository;
use App\Repositories\UserRepository;
use App\Services\MediaService;
use App\Services\UserService;
use Carbon\Carbon;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\RateLimiter;

class RegistrationController extends Controller
{
    public function __construct(
        private UserService $userService,
        private UserRepository $userRepository,
        private OTPRepository $otpRepository,
        private CountryRepository $countryRepository,
        private MediaService $mediaService
    ) {}

    public function store(CompleteProfileRequest $request)
    {
        $phone = normalizeMobileNumber($request->phone);

        if (RateLimiter::tooManyAttempts("complete-user-profile:$request->country_code-$phone", 4)) {
            throw new ThrottleRequestsException();
        }

        RateLimiter::hit("complete-user-profile:$request->country_code-$phone", 60);

        $otp = $this->otpRepository->get(User::class, $request->country_code, $phone, $request->code);

        if (!$otp) {
            return errorMessage(__("invalid code"));
        }

        $latest_otp = $this->otpRepository->getLatest(User::class, $request->country_code, $phone);

        if ($latest_otp->code != $request->code) {
            return errorMessage(__('please enter the latest sent code'));
        }

        // expire code after 1 hour
        if (Carbon::parse($latest_otp->created_at)->addHour()->lt(now())) {
            return errorMessage(__('expired code'));
        }

        $data = $request->validated();
        $data['phone'] = $phone;

        $user = DB::transaction(function () use ($data, $request, $phone) {

            $user = $this->userService->register($data);

            // delete all phone otp
            $this->otpRepository->deleteAll(User::class, $request->country_code, $phone);

            return $user;
        });

        $token = $user->createToken('token')->plainTextToken;

        return success([
            'token' => $token,
            'user' => new LoginResource($user)
        ]);
    }
}
