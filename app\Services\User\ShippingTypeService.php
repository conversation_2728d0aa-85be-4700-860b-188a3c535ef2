<?php

namespace App\Services\User;

use App\Repositories\CityRepository;
use App\Repositories\CompanyShippingTypeRepository;
use App\Repositories\CompanyImmediateShippingCityRepository;

class ShippingTypeService
{
    public function __construct(
        private readonly CityRepository $cityRepository,
        private readonly CompanyImmediateShippingCityRepository $companyImmediateShippingCityRepository,
        private readonly CompanyShippingTypeRepository $companyShippingTypeRepository
    ) {
        //
    }

    public function getAvailableShippingTypesForLocation(float $lat, float $lng)
    {
        $city = $this->cityRepository->getByLocation($lat, $lng);

        if (!$city) {
            return [];
        }

        $availableShippingTypes = $this->companyShippingTypeRepository->getAvailableShippingTypesForCity($city);

        return $availableShippingTypes->map(function ($shippingType) {
            return [
                'id' => $shippingType['id'],
                'name' => $shippingType['name'],
                'companies_count' => $shippingType['companies']->count(),
                'companies' => $shippingType['companies']->map(function ($company) {
                    return [
                        'id' => $company->id,
                        'name' => $company->name,
                        'code' => $company->code,
                    ];
                })->values()
            ];
        })->values()->toArray();
    }
}
