<?php

namespace App\Services\User;

use App\Repositories\CityRepository;
use App\Repositories\CompanyImmediateShippingCityRepository;
use App\Repositories\CompanyShippingTypeRepository;

class ShippingTypeService
{
    public function __construct(
        private readonly CityRepository $cityRepository,
        private readonly CompanyImmediateShippingCityRepository $companyImmediateShippingCityRepository,
        private readonly CompanyShippingTypeRepository $companyShippingTypeRepository
    ) {
        //
    }

    public function getAvailableShippingTypesForLocation(float $lat, float $lng)
    {
        $city = $this->cityRepository->getByLocation($lat, $lng);

        if (! $city) {
            return [];
        }

        $availableShippingTypes = $this->companyShippingTypeRepository->getAvailableShippingTypesForCity($city);

        return $availableShippingTypes;
    }
}
