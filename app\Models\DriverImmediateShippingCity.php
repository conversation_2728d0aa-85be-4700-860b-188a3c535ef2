<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DriverImmediateShippingCity extends Model
{
    public $timestamps = false;

    protected $fillable = [
        'driver_id',
        'city_id'
    ];


    public function city()
    {
        return $this->belongsTo(City::class);
    }

    public function cityAreas()
    {
        return $this->hasMany(DriverImmediateShippingCityArea::class, 'driver_city_id');
    }
}
