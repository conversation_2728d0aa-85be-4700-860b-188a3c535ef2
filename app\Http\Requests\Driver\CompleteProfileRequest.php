<?php

namespace App\Http\Requests\Driver;

use App\Http\Requests\BaseFormRequest;
use App\Models\Driver;
use App\Rules\ActiveAreas;
use App\Rules\ActiveCities;
use App\Rules\ActiveImmeditateShippingCities;
use App\Rules\ActiveTransportionMethods;
use App\Rules\AreasRequiredWithImmediateShipping;
use App\Rules\DistinctArrayValues;
use App\Rules\UniquePhone;
use App\Rules\UniqueShippingOptionCities;
use App\Rules\UniqueShippingOptionCityAreas;
use App\Rules\UniqueShippingOptionTransportMethods;
use App\Rules\ValidBirthDate;
use App\Rules\ValidMedia;
use App\Rules\ValidPhone;
use App\Services\MediaService;

class CompleteProfileRequest extends BaseFormRequest
{
    public function __construct(private MediaService $mediaService) {}

    public function rules(): array
    {
        return [
            'country_code'  => ['required', 'string', 'exists:countries,code'],
            'phone' => ['required', 'string', new ValidPhone($this->country_code), new UniquePhone($this->country_code, Driver::class)],
            'code' => ['required', 'string'],
            'name' => ['required', 'string', 'min:4', 'max:30'],
            'email' => ['nullable', 'string', 'email:rfc,dns', 'max:255', 'unique:drivers,email'],
            'gender' => ['nullable', 'in:male,female'],
            'birth_date' => ['nullable', new ValidBirthDate],
            'profile_image' => ['nullable', new ValidMedia(['image'])],
            'company_code' => ['required', 'string', 'exists:companies,code'],
            'id_number' => ['nullable', 'string', 'min:10', 'max:15', 'unique:drivers,id_number'],
            'id_number_image' => ['nullable', new ValidMedia(['image', 'pdf'])],
            'driving_licence_image' => ['required', new ValidMedia(['image', 'pdf'])],
            'additional_attachments' => ['nullable', 'array'],
            'additional_attachments.*' => ['nullable', 'file', new ValidMedia(['image', 'pdf'])],
            'bank_name' => ['required', 'string', 'min:5', 'max:100'],
            'bank_account_owner' => ['required', 'string', 'min:5', 'max:100'],
            'bank_account_number' => ['required', 'digits_between:10,20'],
            'iban' => ['nullable', 'string', 'regex:/^[A-Z]{2}\d{2}[A-Z0-9]{11,30}$/i'],
            'shipping_options' => ['required', 'array'],
            'shipping_options.*.shipping_type_id' => ['required', 'exists:shipping_types,id', 'distinct'],
            'shipping_options.*.transportion_methods' => ['required', 'array', new DistinctArrayValues, new ActiveTransportionMethods],
            'shipping_options.*.transportion_methods.*' => ['required', 'exists:transportion_methods,id'],
            'shipping_options.*.cities' => ['required', 'array', new UniqueShippingOptionCities, new ActiveImmeditateShippingCities],
            'shipping_options.*.cities.*.id' => ['required', 'exists:cities,id', new AreasRequiredWithImmediateShipping],
            'shipping_options.*.cities.*.areas' => ['nullable', 'array', new UniqueShippingOptionCityAreas, new ActiveAreas],
            'shipping_options.*.cities.*.areas.*' => ['required', 'exists:areas,id'],
        ];
    }

    public function messages(): array
    {
        return [
            'birth_date.before_or_equal' => __("You must be at least 18 years old to register"),
        ];
    }
}
