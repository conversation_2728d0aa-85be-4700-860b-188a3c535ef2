<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class UniqueShippingOptionCityAreas implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!is_array($value)) {
            return;
        }

        $seen = [];
        $duplicates = [];

        foreach ($value as $areaId) {
            if (isset($seen[$areaId])) {
                $duplicates[] = $areaId;
            } else {
                $seen[$areaId] = true;
            }
        }

        if (!empty($duplicates)) {
            $fail("Duplicate areas found in {$attribute}.");
        }
    }
}
