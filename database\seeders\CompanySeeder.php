<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\Country;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CompanySeeder extends Seeder
{
    public function run(): void
    {
        for ($i = 1; $i <= 5; $i++) {

            $country = Country::find(fake()->randomElement([1, 2]));

            $Company = Company::create([
                'name' => "Company $i",
                'country_id' => $country->id,
                'country_code' => $country->code,
                'email' => fake()->optional()->companyEmail(),
                'business_registration_number' => fake()->unique()->numerify('BRN########'),
                'phone' => "*********$i",
                'bank' => fake()->company(),
                'bank_account_owner' => "Owner $i",
                'bank_account_number' => fake()->bankAccountNumber(),
                'iban' => fake()->optional()->iban('US'),
                'shipment_size' => fake()->randomElement(['small', 'bulk', 'both']),
            ]);
        }
    }
}
