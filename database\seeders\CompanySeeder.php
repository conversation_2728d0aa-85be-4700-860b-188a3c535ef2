<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\Country;
use Illuminate\Database\Seeder;

class CompanySeeder extends Seeder
{
    public function run(): void
    {
        $ue = Country::find(1);

        $company = Company::create([
            'name' => "الأندلسية",
            'country_code' => $ue->code,
            'email' => fake()->companyEmail(),
            'phone' => "*********",
            'business_registration_number' => fake()->unique()->numerify('BRN########'),
            'code' => "CM-500",
            'bank_name' => fake()->company(),
            'bank_account_owner' => fake()->name(),
            'bank_account_number' => fake()->bankAccountNumber(),
            'iban' => fake()->optional()->iban('UE'),
            'approval_status' => 'approved'
        ]);

        // Immediate Shipping

        $immediate_shipping = $company->shippingTypes()->create(['shipping_type_id' => 1]);

        $size1 = $immediate_shipping->sizes()->create(['shipping_size_id' => 1]);
        $size2 = $immediate_shipping->sizes()->create(['shipping_size_id' => 2]);

        $transportion_method1 = $size1->transportionMethods()->create(['company_shipping_type_id' => 1, 'transportion_method_id' => 1]);
        $transportion_method2 =  $size1->transportionMethods()->create(['company_shipping_type_id' => 1, 'transportion_method_id' => 2]);

        $transportion_method3 = $size2->transportionMethods()->create(['company_shipping_type_id' => 1, 'transportion_method_id' => 2]);
        $transportion_method4 = $size2->transportionMethods()->create(['company_shipping_type_id' => 1, 'transportion_method_id' => 3]);


        $transportion_method1->vehicles()->create(['company_id' => $company->id, 'model' => 'Mercedes', 'year' => 2022]);
        $transportion_method1->vehicles()->create(['company_id' => $company->id, 'model' => 'BMW', 'year' => 2022]);

        $transportion_method2->vehicles()->create(['company_id' => $company->id, 'model' => 'Chevolet', 'year' => 2004]);
        $transportion_method2->vehicles()->create(['company_id' => $company->id, 'model' => 'Fiat', 'year' => 2014]);

        $transportion_method3->vehicles()->create(['company_id' => $company->id, 'model' => 'Oprta', 'year' => 2010]);
        $transportion_method3->vehicles()->create(['company_id' => $company->id, 'model' => 'Suezuki', 'year' => 2008]);

        $transportion_method4->vehicles()->create(['company_id' => $company->id, 'model' => 'Maxie', 'year' => 2020]);
        $transportion_method4->vehicles()->create(['company_id' => $company->id, 'model' => 'Toyota', 'year' => 2016]);

        $city1 = $company->immediateShippingCities()->create(['city_id' => 1]);
        $city2 = $company->immediateShippingCities()->create(['city_id' => 2]);

        $city1->areas()->attach([1, 2, 3]);
        $city2->areas()->attach([6, 7, 8]);

        // -----------------------------------------------------------------------
        // -----------------------------------------------------------------------

        // Intercity Shipping

        $intercity_shipping = $company->shippingTypes()->create(['shipping_type_id' => 2]);

        $size1 = $intercity_shipping->sizes()->create(['shipping_size_id' => 3]);
        $size2 = $intercity_shipping->sizes()->create(['shipping_size_id' => 4]);

        $transportion_method1 = $size1->transportionMethods()->create(['company_shipping_type_id' => 2, 'transportion_method_id' => 1]);
        $transportion_method2 = $size1->transportionMethods()->create(['company_shipping_type_id' => 2, 'transportion_method_id' => 2]);

        $transportion_method3 = $size2->transportionMethods()->create(['company_shipping_type_id' => 2, 'transportion_method_id' => 2]);
        $transportion_method4 = $size2->transportionMethods()->create(['company_shipping_type_id' => 2, 'transportion_method_id' => 3]);


        $transportion_method1->vehicles()->create(['company_id' => $company->id, 'model' => 'Mercedes', 'year' => 2022]);
        $transportion_method1->vehicles()->create(['company_id' => $company->id, 'model' => 'BMW', 'year' => 2022]);

        $transportion_method2->vehicles()->create(['company_id' => $company->id, 'model' => 'Chevolet', 'year' => 2004]);
        $transportion_method2->vehicles()->create(['company_id' => $company->id, 'model' => 'Fiat', 'year' => 2014]);

        $transportion_method3->vehicles()->create(['company_id' => $company->id, 'model' => 'Oprta', 'year' => 2010]);
        $transportion_method3->vehicles()->create(['company_id' => $company->id, 'model' => 'Suezuki', 'year' => 2008]);

        $transportion_method4->vehicles()->create(['company_id' => $company->id, 'model' => 'Maxie', 'year' => 2020]);
        $transportion_method4->vehicles()->create(['company_id' => $company->id, 'model' => 'Toyota', 'year' => 2016]);

        $company->intercityShippingPickupCities()->attach([1, 2], ['type' => 'pickup']);
        $company->intercityShippingDeliveryCities()->attach([3, 4, 5], ['type' => 'delivery']);

        // -----------------------------------------------------------------------
        // -----------------------------------------------------------------------

        // International Shipping

        $international_shipping = $company->shippingTypes()->create(['shipping_type_id' => 3]);

        $size1 = $international_shipping->sizes()->create(['shipping_size_id' => 2]);
        $size2 = $international_shipping->sizes()->create(['shipping_size_id' => 3]);

        $transportion_method1 = $size1->transportionMethods()->create(['company_shipping_type_id' => 3, 'transportion_method_id' => 1]);
        $transportion_method2 =  $size1->transportionMethods()->create(['company_shipping_type_id' => 3, 'transportion_method_id' => 2]);

        $transportion_method3 = $size2->transportionMethods()->create(['company_shipping_type_id' => 3, 'transportion_method_id' => 2]);
        $transportion_method4 = $size2->transportionMethods()->create(['company_shipping_type_id' => 3, 'transportion_method_id' => 3]);
        $transportion_method4 = $size2->transportionMethods()->create(['company_shipping_type_id' => 3, 'transportion_method_id' => 1]);


        $transportion_method1->vehicles()->create(['company_id' => $company->id, 'model' => 'Mercedes', 'year' => 2022]);
        $transportion_method1->vehicles()->create(['company_id' => $company->id, 'model' => 'BMW', 'year' => 2022]);

        $transportion_method2->vehicles()->create(['company_id' => $company->id, 'model' => 'Chevolet', 'year' => 2004]);
        $transportion_method2->vehicles()->create(['company_id' => $company->id, 'model' => 'Fiat', 'year' => 2014]);

        $transportion_method3->vehicles()->create(['company_id' => $company->id, 'model' => 'Oprta', 'year' => 2010]);
        $transportion_method3->vehicles()->create(['company_id' => $company->id, 'model' => 'Suezuki', 'year' => 2008]);

        $transportion_method4->vehicles()->create(['company_id' => $company->id, 'model' => 'Maxie', 'year' => 2020]);
        $transportion_method4->vehicles()->create(['company_id' => $company->id, 'model' => 'Toyota', 'year' => 2016]);

        $company->internationalShippingCountires()->attach([2, 3, 5]);

        $company->internationalShippingCities()->attach([2, 4, 6]);
    }
}
