<?php

namespace App\Services;

use App\Repositories\CompanyRepository;
use App\Repositories\CompanyShippingTypeRepository;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class CompanyShippingTypeService
{
    public function __construct(
        private CompanyRepository $companyRepository,
        private CompanyShippingTypeRepository $companyShippingTypeRepository,
    ) {}

    public function getCompanyShippingTypesForDriver(string $company_code)
    {
        $company = $this->companyRepository->getByCode($company_code);

        if(!$company) {
            throw new NotFoundHttpException();
        }

        return $this->companyShippingTypeRepository->getCompanyShippingTypes($company);
    }
}
