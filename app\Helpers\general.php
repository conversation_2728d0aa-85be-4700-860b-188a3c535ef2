<?php

use Carbon\Carbon;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Str;

function successMessage(string $message)
{
    return response()->json([
        'message' => $message
    ], 200);
}

function errorMessage(string $message, $status = 400)
{
    return response()->json([
        'message' => $message
    ], $status);
}

function success($data = [], $message = null)
{
    $data = [
        'data' => $data,
    ];

    if ($message) {
        $data['message'] = $message;
    }

    return response()->json($data, 200);
}

function formatDate(string $date)
{
    return Carbon::parse($date)->format('Y-m-d H:i:s');
}

function normalizeMobileNumber($number)
{
    return ltrim($number, '0');
}

function restoreInvalidatedValue($value)
{
    $delimiter = '_del_';
    $pos = strrpos($value, $delimiter);

    return $pos !== false ? substr($value, 0, $pos) : $value;
}

function formatSecondsToMinutesTime($seconds)
{
    $minutes = floor($seconds / 60);
    $remainingSeconds = $seconds % 60;

    return sprintf("%02d:%02d", $minutes, $remainingSeconds);
}

function formatSecondsToHoursTime($seconds)
{
    $hours = floor($seconds / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    $remainingSeconds = $seconds % 60;

    return sprintf("%02d:%02d:%02d", $hours, $minutes, $remainingSeconds);
}

function generatePassCode()
{
    return App::environment('local') ? '123456' : fake()->randomNumber(6, true);
}