<?php

namespace App\Repositories;

use App\Models\City;
use App\Models\Company;
use App\Models\CompanyShippingType;
use Illuminate\Support\Collection;

class CompanyShippingTypeRepository
{
    public function __construct(private CompanyShippingType $model) {}

    public function create(Company $company, string $shipping_type_id, ?bool $has_express_delivery = null)
    {
        return $this->model->create([
            'company_id' => $company->id,
            'shipping_type_id' => $shipping_type_id,
            'has_express_delivery' => $has_express_delivery,
        ]);
    }

    public function get(Company $company, string $shipping_type_id)
    {
        return $this->model->where(['company_id' => $company->id, 'shipping_type_id' => $shipping_type_id])->first();
    }

    public function getCompanyShippingTypes(Company $company)
    {
        return $this->model
            ->where('company_id', $company->id)
            ->with([
                'company.immediateShippingCities' => function ($query) {
                    $query->whereHas('city', fn($q) => $q->where('status', 'active'))
                        ->with([
                            'city' => fn($q) => $q->where('status', 'active')->withTranslation(),
                            'areas' => fn($q) => $q->where('status', 'active')->withTranslation(),
                        ]);
                },
                'company.intercityShippingPickupCities' => fn($query) => $query->where('status', 'active')->withTranslation(),
                'company.internationalShippingCities' => fn($query) => $query->where('status', 'active')->withTranslation(),
                'shippingType',
                'transportionMethods' => fn($query) => $query->where('transportion_methods.status', 'active')->distinct('transportion_methods.id'),
            ])
            ->get();
    }

    public function getAvailableShippingTypesForCity(City $city): Collection
    {
        $availableTypes = collect();

        $shippingTypes = [
            'immediate' => 'company.immediateShippingCities',
            'intercity' => 'company.intercityShippingPickupCities',
            'international' => 'company.internationalShippingCities',
        ];

        foreach ($shippingTypes as $type => $relation) {
            $results = $this->model
                ->whereHas($relation, fn($q) => $q->where('city_id', $city->id))
                ->whereHas('shippingType', fn($q) => $q->where('name', $type))
                ->with('shippingType:id,name')
                ->get();

            if ($results->isNotEmpty()) {
                $shippingType = $results->first()->shippingType;
                $hasExpressDelivery = $results->contains(fn($result) => $result->has_express_delivery === true);

                $availableTypes->push([
                    'id' => $shippingType->id,
                    'name' => $shippingType->name,
                    'has_express_delivery' => $hasExpressDelivery,
                ]);
            }
        }

        return $availableTypes->unique('id')->values();
    }
}
