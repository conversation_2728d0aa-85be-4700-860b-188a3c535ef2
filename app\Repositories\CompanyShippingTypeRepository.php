<?php

namespace App\Repositories;

use App\Models\Company;
use App\Models\CompanyShippingType;

class CompanyShippingTypeRepository
{
    public function __construct(private CompanyShippingType $model) {}

    public function create(Company $company, string $shipping_type_id, ?bool $has_express_delivery = null)
    {
        return $this->model->create([
            'company_id' => $company->id,
            'shipping_type_id' => $shipping_type_id,
            'has_express_delivery' => $has_express_delivery
        ]);
    }

    public function get(Company $company, string $shipping_type_id)
    {
        return $this->model->where(['company_id' => $company->id, 'shipping_type_id' => $shipping_type_id])->first();
    }

    public function getCompanyShippingTypes(Company $company)
    {
        return $this->model
            ->where('company_id', $company->id)
            ->with([
                'company.immediateShippingCities' => function ($query) {
                    $query->whereHas('city', fn($q) => $q->where('status', 'active'))
                        ->with([
                            'city' => fn($q) => $q->where('status', 'active')->withTranslation(),
                            'areas' => fn($q) => $q->where('status', 'active')->withTranslation(),
                        ]);
                },
                'company.intercityShippingPickupCities' => fn($query) => $query->where('status', 'active')->withTranslation(),
                'company.internationalShippingCities' => fn($query) => $query->where('status', 'active')->withTranslation(),
                'shippingType',
                'transportionMethods' => fn($query) => $query->where('transportion_methods.status', 'active')->distinct('transportion_methods.id'),
            ])
            ->get();
    }

    public function getAvailableShippingTypesForCity($city)
    {
        $availableTypes = collect();

        // Get immediate shipping types
        $immediateShipping = $this->model
            ->whereHas('company.immediateShippingCities', function ($query) use ($city) {
                $query->where('city_id', $city->id);
            })
            ->with(['shippingType', 'company'])
            ->where('shipping_type_id', 1)
            ->get();

        if ($immediateShipping->isNotEmpty()) {
            $availableTypes->push([
                'id' => 1,
                'name' => 'immediate',
                'companies' => $immediateShipping->pluck('company')->unique('id')->values()
            ]);
        }

        // Get intercity shipping types (pickup cities)
        $intercityShipping = $this->model
            ->whereHas('company.intercityShippingPickupCities', function ($query) use ($city) {
                $query->where('city_id', $city->id);
            })
            ->with(['shippingType', 'company'])
            ->where('shipping_type_id', 2)
            ->get();

        if ($intercityShipping->isNotEmpty()) {
            $availableTypes->push([
                'id' => 2,
                'name' => 'intercity',
                'companies' => $intercityShipping->pluck('company')->unique('id')->values()
            ]);
        }

        // Get international shipping types
        $internationalShipping = $this->model
            ->whereHas('company.internationalShippingCities', function ($query) use ($city) {
                $query->where('city_id', $city->id);
            })
            ->with(['shippingType', 'company'])
            ->where('shipping_type_id', 3)
            ->get();

        if ($internationalShipping->isNotEmpty()) {
            $availableTypes->push([
                'id' => 3,
                'name' => 'international',
                'companies' => $internationalShipping->pluck('company')->unique('id')->values()
            ]);
        }

        return $availableTypes;
    }
}
