<?php

namespace App\Repositories;

use App\Models\Company;
use App\Models\CompanyShippingType;

class CompanyShippingTypeRepository
{
    public function __construct(private CompanyShippingType $model) {}

    public function create(Company $company, string $shipping_type_id, ?bool $has_express_delivery = null)
    {
        return $this->model->create([
            'company_id' => $company->id,
            'shipping_type_id' => $shipping_type_id,
            'has_express_delivery' => $has_express_delivery
        ]);
    }

    public function get(Company $company, string $shipping_type_id)
    {
        return $this->model->where(['company_id' => $company->id, 'shipping_type_id' => $shipping_type_id])->first();
    }

    public function getCompanyShippingTypes(Company $company)
    {
        return $this->model
            ->where('company_id', $company->id)
            ->with([
                'company.immediateShippingCities' => function ($query) {
                    $query->whereHas('city', fn($q) => $q->where('status', 'active'))
                        ->with([
                            'city' => fn($q) => $q->where('status', 'active')->withTranslation(),
                            'areas' => fn($q) => $q->where('status', 'active')->withTranslation(),
                        ]);
                },
                'company.intercityShippingPickupCities' => fn($query) => $query->where('status', 'active')->withTranslation(),
                'company.internationalShippingCities' => fn($query) => $query->where('status', 'active')->withTranslation(),
                'shippingType',
                'transportionMethods' => fn($query) => $query->where('transportion_methods.status', 'active')->distinct('transportion_methods.id'),
            ])
            ->get();
    }

    public function getAvailableShippingTypesForCity($city)
    {
        $availableTypes = collect();

        // Immediate
        if ($this->model->where('shipping_type_id', 1)
            ->whereHas('company.immediateShippingCities', fn($q) => $q->where('city_id', $city->id))
            ->exists()
        ) {
            $availableTypes->push([
                'id' => 1,
                'name' => 'immediate',
            ]);
        }

        // Intercity
        if ($this->model->where('shipping_type_id', 2)
            ->whereHas('company.intercityShippingPickupCities', fn($q) => $q->where('city_id', $city->id))
            ->exists()
        ) {
            $availableTypes->push([
                'id' => 2,
                'name' => 'intercity',
            ]);
        }

        // International
        if ($this->model->where('shipping_type_id', 3)
            ->whereHas('company.internationalShippingCities', fn($q) => $q->where('city_id', $city->id))
            ->exists()
        ) {
            $availableTypes->push([
                'id' => 3,
                'name' => 'international',
            ]);
        }

        return $availableTypes;
    }
}
