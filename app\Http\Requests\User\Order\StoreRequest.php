<?php

namespace App\Http\Requests\User\Order;

use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'shipping_type_id' => 'required|exists:shipping_types,id',
            'shipping_size_id' => 'required|exists:shipping_sizes,id',
            'pickup_address_id' => 'required|exists:addresses,id',
            'payment_method' => 'required|string',
            'is_express' => 'required|boolean',
            'items' => 'required|array',
            'items.*.shipment_number' => 'required|string',
            'items.*.cost' => 'required|numeric',
            'items.*.fees' => 'required|numeric',
            'items.*.dropoff_address_id' => 'required|exists:addresses,id',
            'items.*.recipient_name' => 'required|string',
            'items.*.country_code' => 'required|string',
            'items.*.phone' => 'required|string',
            'items.*.description' => 'nullable|string',
        ];
    }
}
