<?php

namespace App\Http\Requests\User\Order;

use App\Rules\ValidMedia;
use Illuminate\Validation\Rule;
use App\Enum\OrderPaymentMethod;
use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $userId = auth('user')->id();

        return [
            'shipping_type_id' => 'required|exists:shipping_types,id',
            'shipping_size_id' => 'required|exists:shipping_sizes,id',
            'pickup_address_id' => ['nullable', Rule::exists('addresses', 'id')->where('user_id', auth('user')->id())],
            'pickup_location' => 'nullable|array',
            'pickup_location.lat' => 'required_with:pickup_location|numeric|between:-90,90',
            'pickup_location.lng' => 'required_with:pickup_location|numeric|between:-180,180',
            'payment_method' => ['required', Rule::enum(OrderPaymentMethod::class)],
            'is_express' => 'required|boolean',
            'description' => 'nullable|string|max:1000',
            'items' => 'required|array|min:1',
            'items.*.cost' => 'required|numeric|min:0',
            'items.*.fees' => 'required|numeric|min:0',
            'items.*.dropoff_address_id' => ['nullable', Rule::exists('addresses', 'id')->where('user_id', auth('user')->id())],
            'items.*.dropoff_location' => 'nullable|array',
            'items.*.dropoff_location.lat' => 'required_with:items.*.dropoff_location|numeric|between:-90,90',
            'items.*.dropoff_location.lng' => 'required_with:items.*.dropoff_location|numeric|between:-180,180',
            'items.*.recipient_name' => 'required|string|max:255',
            'items.*.country_code' => 'required|string|max:10',
            'items.*.phone' => 'required|string|max:20',
            'items.*.description' => 'nullable|string|max:1000',
            'items.*.images' => 'required|array|min:1|max:5',
            'items.*.images.*' => ['required', new ValidMedia(['image'])],
        ];
    }

    public function messages(): array
    {
        return [
            'pickup_address_id.required_without' => 'Either pickup address or pickup location is required.',
            'pickup_location.required_without' => 'Either pickup address or pickup location is required.',
            'items.*.dropoff_address_id.required_without' => 'Either dropoff address or dropoff location is required for each item.',
            'items.*.dropoff_location.required_without' => 'Either dropoff address or dropoff location is required for each item.',
            'items.*.shipment_number.unique' => 'Each shipment number must be unique.',
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            if (!$this->pickup_address_id && !$this->pickup_location) {
                $validator->errors()->add('pickup_address_id', 'Either pickup address or pickup location is required.');
            }

            if ($this->items) {
                foreach ($this->items as $index => $item) {
                    if (empty($item['dropoff_address_id']) && empty($item['dropoff_location'])) {
                        $validator->errors()->add("items.{$index}.dropoff_address_id", 'Either dropoff address or dropoff location is required.');
                    }
                }
            }
        });
    }
}
