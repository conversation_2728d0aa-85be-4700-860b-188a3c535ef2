<?php

namespace App\Http\Controllers\Company\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\LoginRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;

class AuthenticationController extends Controller
{
    public function loginView()
    {
        return view('auth.login');
    }

    public function login(LoginRequest $request)
    {
        if (RateLimiter::tooManyAttempts("login-attempts-company:$request->email", 3)) {

            $seconds = RateLimiter::availableIn("login-attempts-company:$request->email");
            $remainingTime = formatSecondsToMinutesTime($seconds);

            return back()->with('fail', __("too many attempts: retry after :time minutes", ['time' => $remainingTime]));
        }

        RateLimiter::hit("login-attempts-company:$request->email", 120);

        $credentials = ['email' => $request->email, 'password' => $request->password];

        if (!auth('company')->attempt($credentials, $request->boolean('remember'))) {
            throw ValidationException::withMessages([
                'email' => __('auth.failed')
            ]);
        }

        // check if belongs to a pending company
        if (auth('company')->user()->company->approval_status == 'pending') {
            auth('company')->logout();
            throw ValidationException::withMessages([
                'email' => __('auth.failed')
            ]);
        }

        // check if account is active
        if (auth('company')->user()->status != 'active') {
            auth('company')->logout();
            $request->session()->invalidate();

            return back()->with('fail', __("your account is disabled"));
        }

        $request->session()->regenerate();

        return redirect()->intended(route('company.index'));
    }

    public function logout(Request $request)
    {
        auth('company')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return to_route('company.login');
    }
}
