<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;

class Country extends Model implements TranslatableContract
{
    use Translatable;

    protected $fillable = [
        'code',
        'abbv'
    ];

    public $translatedAttributes = [
        'name'
    ];


    public function getFlagAttribute()
    {
        $code = strtolower($this->abbv);

        return url("https://flagcdn.com/w80/$code.png");
    }
}
