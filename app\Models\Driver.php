<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Driver extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'name',
        'country_id',
        'country_code',
        'phone',
        'email',
        'birth_date',
        'gender',
        'company_id',
        'city_id',
        'national_id',
        'bank',
        'bank_account_owner',
        'bank_account_number',
        'iban',
        'is_available',
        'push_notifications_enabled'
    ];

    protected $casts = [
        'birth_date' => 'date',
        'is_available' => 'boolean',
        'push_notifications_enabled' => 'boolean',
    ];


    public function getPhoneAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function getEmailAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function getNationalIdAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function profileImage()
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'profile');
    }

    public function idImage()
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'id');
    }

    public function drivingLiscenceImage()
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'driving_liscence');
    }

    public function additionalAttachments()
    {
        return $this->morphMany(Media::class, 'mediable')->where('type', 'additional_attachment');
    }

    public function cities()
    {
        return $this->belongsToMany(City::class, 'driver_areas');
    }

    public function areas()
    {
        return $this->belongsToMany(Area::class, 'driver_areas');
    }
}
