<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use <PERSON>vel\Sanctum\HasApiTokens;

class Driver extends Model
{
    use SoftDeletes, HasApiTokens;

    protected $fillable = [
        'name',
        'country_code',
        'phone',
        'email',
        'birth_date',
        'gender',
        'company_id',
        'city_id',
        'id_number',
        'bank_name',
        'bank_account_owner',
        'bank_account_number',
        'iban',
        'status',
        'approval_status',
        'is_available',
        'push_notifications_enabled'
    ];

    protected $casts = [
        'birth_date' => 'date',
        'is_available' => 'boolean',
        'push_notifications_enabled' => 'boolean',
    ];


    public function getPhoneAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function getEmailAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function getNationalIdAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function profileImage()
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'profile');
    }

    public function idImage()
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'id_number');
    }

    public function drivingLiscenceImage()
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'driving_licence');
    }

    public function additionalAttachments()
    {
        return $this->morphMany(Media::class, 'mediable')->where('type', 'additional');
    }

    public function intercityShippingCities()
    {
        return $this->belongsToMany(City::class, 'driver_shipping_cities', 'driver_id', 'city_id')->where('shipping_type', 'intercity');
    }

    public function internationalShippingCities()
    {
        return $this->belongsToMany(City::class, 'driver_shipping_cities', 'driver_id', 'city_id')->where('shipping_type', 'international');
    }

    public function immediateShippingCities()
    {
        return $this->hasMany(DriverImmediateShippingCity::class, 'driver_id');
    }

    public function shippingTypes()
    {
        return $this->hasMany(DriverShippingType::class);
    }
}
