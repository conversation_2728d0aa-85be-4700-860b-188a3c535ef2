<?php

namespace App\Repositories;

use App\Models\Admin;

class AdminRepository
{
    public function __construct(private Admin $model) {}

    public function getById(string $id)
    {
        return $this->model->find($id);
    }

    public function getByEmail(string $email)
    {
        return $this->model->where('email', $email)->first();
    }

    public function getByPhone($country_code, $phone)
    {
        return $this->model->where('country_code', $country_code)->where('phone', $phone)->first();
    }

    public function updatePassword(Admin $admin, string $password)
    {
        $admin->update([
            'password' => bcrypt($password)
        ]);
    }
}
