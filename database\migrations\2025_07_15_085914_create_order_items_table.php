<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->id();

            $table->foreignId('order_id')->constrained()->cascadeOnDelete();
            $table->string('shipment_number')->unique();
            $table->decimal('cost', 10, 2)->default(0);
            $table->decimal('fees', 10, 2)->default(0);
            $table->foreignId('dropoff_address_id')->nullable()->constrained('addresses');
            $table->geometry('dropoff_location', subtype: 'point')->nullable();
            $table->string('recipient_name');
            $table->string('country_code');
            $table->string('phone');
            $table->text('description')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_items');
    }
};
