<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\CompanyAdmin;
use App\Models\Country;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CompanyAdminSeeder extends Seeder
{
    public function run(): void
    {
        $companies = Company::get();

        foreach ($companies as $company) {
            $country = Country::find(fake()->randomElement([1, 2]));

            for ($i = 1; $i <= 3; $i++) {
                $admin = CompanyAdmin::create([
                    'company_id' => $company->id,
                    'name' => "Company Admin $i for {$company->name}",
                    'country_id' => $country->id,
                    'country_code' => $country->code,
                    'phone' => fake()->numerify('##########'),
                    'email' => "admin{$i}.company{$company->id}@packz.com",
                    'role_id' => $i == 1 ? 1 : 2,
                ]);

                $admin->permissions()->attach($admin->role->permissions);
            }
        }
    }
}
