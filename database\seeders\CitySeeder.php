<?php

namespace Database\Seeders;

use App\Models\Area;
use App\Models\City;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use MatanYadaev\EloquentSpatial\Objects\LineString;
use MatanYadaev\EloquentSpatial\Objects\Point;
use MatanYadaev\EloquentSpatial\Objects\Polygon;

class CitySeeder extends Seeder
{
    public function run()
    {
        // disable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::table('cities')->truncate();
        DB::table('areas')->truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // Define cities with translations and their areas
        $cities = [
            [
                'translations' => [
                    'en' => ['name' => 'Abu Dhabi'],
                    'ar' => ['name' => 'أبوظبي'],
                    'ur' => ['name' => 'ابوظہبی'],
                ],
                'location' => new Point(24.4539, 54.3773), // City center coordinates
                'area' => new Polygon([
                    new LineString([
                        new Point(24.7, 54.2), // Outer boundary of Abu Dhabi
                        new Point(24.7, 54.6),
                        new Point(24.2, 54.6),
                        new Point(24.2, 54.2),
                        new Point(24.7, 54.2),
                    ]),
                ]),
                'areas' => [
                    [
                        'translations' => [
                            'en' => ['name' => 'Al Reem Island'],
                            'ar' => ['name' => 'جزيرة الريم'],
                            'ur' => ['name' => 'الریم آئی لینڈ'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(24.5, 54.4), // Al Reem Island is inside Abu Dhabi
                                new Point(24.5, 54.42),
                                new Point(24.48, 54.42),
                                new Point(24.48, 54.4),
                                new Point(24.5, 54.4),
                            ]),
                        ]),
                    ],
                    [
                        'translations' => [
                            'en' => ['name' => 'Khalifa City'],
                            'ar' => ['name' => 'مدينة خليفة'],
                            'ur' => ['name' => 'خلیفہ سٹی'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(24.42, 54.55), // Khalifa City is inside Abu Dhabi
                                new Point(24.42, 54.58),
                                new Point(24.39, 54.58),
                                new Point(24.39, 54.55),
                                new Point(24.42, 54.55),
                            ]),
                        ]),
                    ],
                    [
                        'translations' => [
                            'en' => ['name' => 'Saadiyat Island'],
                            'ar' => ['name' => 'جزيرة السعديات'],
                            'ur' => ['name' => 'سعدیات آئی لینڈ'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(24.55, 54.43), // Saadiyat Island is inside Abu Dhabi
                                new Point(24.55, 54.45),
                                new Point(24.53, 54.45),
                                new Point(24.53, 54.43),
                                new Point(24.55, 54.43),
                            ]),
                        ]),
                    ],
                    [
                        'translations' => [
                            'en' => ['name' => 'Yas Island'],
                            'ar' => ['name' => 'جزيرة ياس'],
                            'ur' => ['name' => 'یاس آئی لینڈ'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(24.48, 54.6), // Yas Island is inside Abu Dhabi
                                new Point(24.48, 54.62),
                                new Point(24.46, 54.62),
                                new Point(24.46, 54.6),
                                new Point(24.48, 54.6),
                            ]),
                        ]),
                    ],
                    [
                        'translations' => [
                            'en' => ['name' => 'Al Bateen'],
                            'ar' => ['name' => 'البطين'],
                            'ur' => ['name' => 'البطین'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(24.45, 54.32), // Al Bateen is inside Abu Dhabi
                                new Point(24.45, 54.34),
                                new Point(24.43, 54.34),
                                new Point(24.43, 54.32),
                                new Point(24.45, 54.32),
                            ]),
                        ]),
                    ],
                ],
            ],
            [
                'translations' => [
                    'en' => ['name' => 'Dubai'],
                    'ar' => ['name' => 'دبي'],
                    'ur' => ['name' => 'دبئی'],
                ],
                'location' => new Point(25.2048, 55.2708), // Dubai city center
                'area' => new Polygon([
                    new LineString([
                        new Point(25.4, 55.0), // Outer boundary of Dubai
                        new Point(25.4, 55.5),
                        new Point(25.0, 55.5),
                        new Point(25.0, 55.0),
                        new Point(25.4, 55.0),
                    ]),
                ]),
                'areas' => [
                    [
                        'translations' => [
                            'en' => ['name' => 'Downtown Dubai'],
                            'ar' => ['name' => 'وسط مدينة دبي'],
                            'ur' => ['name' => 'ڈاؤن ٹاؤن دبئی'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(25.19, 55.27), // Downtown Dubai is inside Dubai
                                new Point(25.19, 55.29),
                                new Point(25.17, 55.29),
                                new Point(25.17, 55.27),
                                new Point(25.19, 55.27),
                            ]),
                        ]),
                    ],
                    [
                        'translations' => [
                            'en' => ['name' => 'Jumeirah'],
                            'ar' => ['name' => 'جميرا'],
                            'ur' => ['name' => 'جمیرہ'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(25.22, 55.24), // Jumeirah is inside Dubai
                                new Point(25.22, 55.26),
                                new Point(25.20, 55.26),
                                new Point(25.20, 55.24),
                                new Point(25.22, 55.24),
                            ]),
                        ]),
                    ],
                    [
                        'translations' => [
                            'en' => ['name' => 'Dubai Marina'],
                            'ar' => ['name' => 'دبي مارينا'],
                            'ur' => ['name' => 'دبئی مرینہ'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(25.08, 55.13), // Dubai Marina is inside Dubai
                                new Point(25.08, 55.15),
                                new Point(25.06, 55.15),
                                new Point(25.06, 55.13),
                                new Point(25.08, 55.13),
                            ]),
                        ]),
                    ],
                    [
                        'translations' => [
                            'en' => ['name' => 'Palm Jumeirah'],
                            'ar' => ['name' => 'نخلة جميرا'],
                            'ur' => ['name' => 'پام جمیرہ'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(25.11, 55.13), // Palm Jumeirah is inside Dubai
                                new Point(25.11, 55.15),
                                new Point(25.09, 55.15),
                                new Point(25.09, 55.13),
                                new Point(25.11, 55.13),
                            ]),
                        ]),
                    ],
                    [
                        'translations' => [
                            'en' => ['name' => 'Business Bay'],
                            'ar' => ['name' => 'خليج الأعمال'],
                            'ur' => ['name' => 'بزنس بے'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(25.18, 55.26), // Business Bay is inside Dubai
                                new Point(25.18, 55.28),
                                new Point(25.16, 55.28),
                                new Point(25.16, 55.26),
                                new Point(25.18, 55.26),
                            ]),
                        ]),
                    ],
                    [
                        'translations' => [
                            'en' => ['name' => 'Deira'],
                            'ar' => ['name' => 'ديرة'],
                            'ur' => ['name' => 'دیرہ'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(25.27, 55.30), // Deira is inside Dubai
                                new Point(25.27, 55.32),
                                new Point(25.25, 55.32),
                                new Point(25.25, 55.30),
                                new Point(25.27, 55.30),
                            ]),
                        ]),
                    ],
                ],
            ],
            [
                'translations' => [
                    'en' => ['name' => 'Sharjah'],
                    'ar' => ['name' => 'الشارقة'],
                    'ur' => ['name' => 'شارجہ'],
                ],
                'location' => new Point(25.3463, 55.4209), // Sharjah city center
                'area' => new Polygon([
                    new LineString([
                        new Point(25.4, 55.3), // Outer boundary of Sharjah
                        new Point(25.4, 55.6),
                        new Point(25.2, 55.6),
                        new Point(25.2, 55.3),
                        new Point(25.4, 55.3),
                    ]),
                ]),
                'areas' => [
                    [
                        'translations' => [
                            'en' => ['name' => 'Al Majaz'],
                            'ar' => ['name' => 'المجاز'],
                            'ur' => ['name' => 'المجاز'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(25.32, 55.39), // Al Majaz is inside Sharjah
                                new Point(25.32, 55.41),
                                new Point(25.30, 55.41),
                                new Point(25.30, 55.39),
                                new Point(25.32, 55.39),
                            ]),
                        ]),
                    ],
                    [
                        'translations' => [
                            'en' => ['name' => 'Al Nahda'],
                            'ar' => ['name' => 'النهدة'],
                            'ur' => ['name' => 'النہدہ'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(25.31, 55.37), // Al Nahda is inside Sharjah
                                new Point(25.31, 55.39),
                                new Point(25.29, 55.39),
                                new Point(25.29, 55.37),
                                new Point(25.31, 55.37),
                            ]),
                        ]),
                    ],
                    [
                        'translations' => [
                            'en' => ['name' => 'Al Qasimia'],
                            'ar' => ['name' => 'القاسمية'],
                            'ur' => ['name' => 'القاسمیہ'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(25.35, 55.40), // Al Qasimia is inside Sharjah
                                new Point(25.35, 55.42),
                                new Point(25.33, 55.42),
                                new Point(25.33, 55.40),
                                new Point(25.35, 55.40),
                            ]),
                        ]),
                    ],
                    [
                        'translations' => [
                            'en' => ['name' => 'Muwaileh'],
                            'ar' => ['name' => 'مويلح'],
                            'ur' => ['name' => 'موویلہ'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(25.28, 55.45), // Muwaileh is inside Sharjah
                                new Point(25.28, 55.47),
                                new Point(25.26, 55.47),
                                new Point(25.26, 55.45),
                                new Point(25.28, 55.45),
                            ]),
                        ]),
                    ],
                ],
            ],
            [
                'translations' => [
                    'en' => ['name' => 'Ajman'],
                    'ar' => ['name' => 'عجمان'],
                    'ur' => ['name' => 'عجمان'],
                ],
                'location' => new Point(25.4111, 55.4354), // Ajman city center
                'area' => new Polygon([
                    new LineString([
                        new Point(25.45, 55.4), // Outer boundary of Ajman
                        new Point(25.45, 55.5),
                        new Point(25.35, 55.5),
                        new Point(25.35, 55.4),
                        new Point(25.45, 55.4),
                    ]),
                ]),
                'areas' => [
                    [
                        'translations' => [
                            'en' => ['name' => 'Al Nuaimia'],
                            'ar' => ['name' => 'النعيمية'],
                            'ur' => ['name' => 'النعیمیہ'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(25.42, 55.44), // Al Nuaimia is inside Ajman
                                new Point(25.42, 55.46),
                                new Point(25.40, 55.46),
                                new Point(25.40, 55.44),
                                new Point(25.42, 55.44),
                            ]),
                        ]),
                    ],
                    [
                        'translations' => [
                            'en' => ['name' => 'Al Jurf'],
                            'ar' => ['name' => 'الجرف'],
                            'ur' => ['name' => 'الجرف'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(25.41, 55.47), // Al Jurf is inside Ajman
                                new Point(25.41, 55.49),
                                new Point(25.39, 55.49),
                                new Point(25.39, 55.47),
                                new Point(25.41, 55.47),
                            ]),
                        ]),
                    ],
                    [
                        'translations' => [
                            'en' => ['name' => 'Al Rashidiya'],
                            'ar' => ['name' => 'الراشدية'],
                            'ur' => ['name' => 'الراشدیہ'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(25.43, 55.42), // Al Rashidiya is inside Ajman
                                new Point(25.43, 55.44),
                                new Point(25.41, 55.44),
                                new Point(25.41, 55.42),
                                new Point(25.43, 55.42),
                            ]),
                        ]),
                    ],
                ],
            ],
            [
                'translations' => [
                    'en' => ['name' => 'Umm Al Quwain'],
                    'ar' => ['name' => 'أم القيوين'],
                    'ur' => ['name' => 'ام القوین'],
                ],
                'location' => new Point(25.5647, 55.5552), // Umm Al Quwain city center
                'area' => new Polygon([
                    new LineString([
                        new Point(25.6, 55.5), // Outer boundary of Umm Al Quwain
                        new Point(25.6, 55.7),
                        new Point(25.5, 55.7),
                        new Point(25.5, 55.5),
                        new Point(25.6, 55.5),
                    ]),
                ]),
                'areas' => [
                    [
                        'translations' => [
                            'en' => ['name' => 'Al Salamah'],
                            'ar' => ['name' => 'السلامة'],
                            'ur' => ['name' => 'السلامہ'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(25.56, 55.55), // Al Salamah is inside Umm Al Quwain
                                new Point(25.56, 55.57),
                                new Point(25.54, 55.57),
                                new Point(25.54, 55.55),
                                new Point(25.56, 55.55),
                            ]),
                        ]),
                    ],
                    [
                        'translations' => [
                            'en' => ['name' => 'Al Raudah'],
                            'ar' => ['name' => 'الروضة'],
                            'ur' => ['name' => 'الروضہ'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(25.57, 55.58), // Al Raudah is inside Umm Al Quwain
                                new Point(25.57, 55.60),
                                new Point(25.55, 55.60),
                                new Point(25.55, 55.58),
                                new Point(25.57, 55.58),
                            ]),
                        ]),
                    ],
                    [
                        'translations' => [
                            'en' => ['name' => 'Old Town'],
                            'ar' => ['name' => 'البلدة القديمة'],
                            'ur' => ['name' => 'پرانا شہر'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(25.56, 55.52), // Old Town is inside Umm Al Quwain
                                new Point(25.56, 55.54),
                                new Point(25.54, 55.54),
                                new Point(25.54, 55.52),
                                new Point(25.56, 55.52),
                            ]),
                        ]),
                    ],
                ],
            ],
            [
                'translations' => [
                    'en' => ['name' => 'Ras Al Khaimah'],
                    'ar' => ['name' => 'رأس الخيمة'],
                    'ur' => ['name' => 'راس الخیمہ'],
                ],
                'location' => new Point(25.7889, 55.9414), // Ras Al Khaimah city center
                'area' => new Polygon([
                    new LineString([
                        new Point(25.9, 55.8), // Outer boundary of Ras Al Khaimah
                        new Point(25.9, 56.1),
                        new Point(25.6, 56.1),
                        new Point(25.6, 55.8),
                        new Point(25.9, 55.8),
                    ]),
                ]),
                'areas' => [
                    [
                        'translations' => [
                            'en' => ['name' => 'Al Nakheel'],
                            'ar' => ['name' => 'النخيل'],
                            'ur' => ['name' => 'النخیل'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(25.78, 55.93), // Al Nakheel is inside Ras Al Khaimah
                                new Point(25.78, 55.95),
                                new Point(25.76, 55.95),
                                new Point(25.76, 55.93),
                                new Point(25.78, 55.93),
                            ]),
                        ]),
                    ],
                    [
                        'translations' => [
                            'en' => ['name' => 'Al Hamra'],
                            'ar' => ['name' => 'الحمراء'],
                            'ur' => ['name' => 'الحمرا'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(25.68, 55.88), // Al Hamra is inside Ras Al Khaimah
                                new Point(25.68, 55.90),
                                new Point(25.66, 55.90),
                                new Point(25.66, 55.88),
                                new Point(25.68, 55.88),
                            ]),
                        ]),
                    ],
                    [
                        'translations' => [
                            'en' => ['name' => 'Dafan Al Nakheel'],
                            'ar' => ['name' => 'دفان النخيل'],
                            'ur' => ['name' => 'دفان النخیل'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(25.80, 55.96), // Dafan Al Nakheel is inside Ras Al Khaimah
                                new Point(25.80, 55.98),
                                new Point(25.78, 55.98),
                                new Point(25.78, 55.96),
                                new Point(25.80, 55.96),
                            ]),
                        ]),
                    ],
                ],
            ],
            [
                'translations' => [
                    'en' => ['name' => 'Fujairah'],
                    'ar' => ['name' => 'الفجيرة'],
                    'ur' => ['name' => 'فجیرہ'],
                ],
                'location' => new Point(25.1164, 56.3257), // Fujairah city center
                'area' => new Polygon([
                    new LineString([
                        new Point(25.3, 56.2), // Outer boundary of Fujairah
                        new Point(25.3, 56.5),
                        new Point(24.9, 56.5),
                        new Point(24.9, 56.2),
                        new Point(25.3, 56.2),
                    ]),
                ]),
                'areas' => [
                    [
                        'translations' => [
                            'en' => ['name' => 'Al Faseel'],
                            'ar' => ['name' => 'الفصيل'],
                            'ur' => ['name' => 'الفصیل'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(25.11, 56.32), // Al Faseel is inside Fujairah
                                new Point(25.11, 56.34),
                                new Point(25.09, 56.34),
                                new Point(25.09, 56.32),
                                new Point(25.11, 56.32),
                            ]),
                        ]),
                    ],
                    [
                        'translations' => [
                            'en' => ['name' => 'Murbah'],
                            'ar' => ['name' => 'مربح'],
                            'ur' => ['name' => 'مربح'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(25.13, 56.35), // Murbah is inside Fujairah
                                new Point(25.13, 56.37),
                                new Point(25.11, 56.37),
                                new Point(25.11, 56.35),
                                new Point(25.13, 56.35),
                            ]),
                        ]),
                    ],
                    [
                        'translations' => [
                            'en' => ['name' => 'Dibba'],
                            'ar' => ['name' => 'دبا'],
                            'ur' => ['name' => 'دبا'],
                        ],
                        'area' => new Polygon([
                            new LineString([
                                new Point(25.59, 56.26), // Dibba is inside Fujairah
                                new Point(25.59, 56.28),
                                new Point(25.57, 56.28),
                                new Point(25.57, 56.26),
                                new Point(25.59, 56.26),
                            ]),
                        ]),
                    ],
                ],
            ],
        ];

        // Seed cities and their areas
        foreach ($cities as $cityData) {
            // Create city with spatial data
            $city = City::create([
                'location' => $cityData['location'],
                'area' => $cityData['area'],
            ]);

            foreach ($cityData['translations'] as $locale => $translation) {
                $city->translateOrNew($locale)->fill($translation)->save();
            }

            // Create areas for the city with spatial data
            foreach ($cityData['areas'] as $areaData) {
                $area = Area::create([
                    'city_id' => $city->id,
                    'area' => $areaData['area'],
                ]);

                foreach ($areaData['translations'] as $locale => $translation) {
                    $area->translateOrNew($locale)->fill($translation)->save();
                }
            }
        }
    }
}
