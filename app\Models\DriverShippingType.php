<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DriverShippingType extends Model
{
    public $timestamps = false;

    protected $fillable = [
        'driver_id',
        'shipping_type_id'
    ];


    public function type()
    {
        return $this->belongsTo(ShippingType::class, 'shipping_type_id');
    }

    public function transportionMethods()
    {
        return $this->belongsToMany(TransportionMethod::class, 'driver_shipping_type_transportion_methods', 'driver_shipping_type_id', 'transportion_method_id');
    }
}
