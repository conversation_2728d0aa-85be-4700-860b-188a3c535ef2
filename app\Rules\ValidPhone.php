<?php

namespace App\Rules;

use App\Repositories\CountryRepository;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use libphonenumber\PhoneNumberUtil;

class ValidPhone implements ValidationRule
{
    public function __construct(private ?string $country_code = null) {}

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!$this->country_code) {
            return;
        }

        $countryRepository = app(CountryRepository::class);
        $country = $countryRepository->getByCode($this->country_code);

        if (!$country) {
            return;
        }

        $phoneUtil = PhoneNumberUtil::getInstance();

        try {
            $phoneNumber = $phoneUtil->parse($value, $country->abbv);

            if (!$phoneUtil->isValidNumber($phoneNumber)) {
                $fail(__("not a valid phone number"));
            }
        } catch (\Exception $e) {
            $fail(__("not a valid phone number"));
        }
    }
}
