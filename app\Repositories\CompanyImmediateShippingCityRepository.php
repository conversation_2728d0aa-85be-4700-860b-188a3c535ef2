<?php

namespace App\Repositories;

use App\Models\Company;
use App\Models\CompanyImmediateShippingCity;

class CompanyImmediateShippingCityRepository
{
    public function __construct(private CompanyImmediateShippingCity $model) {}

    public function create(Company $company, string $city_id)
    {
        return $this->model->create([
            'company_id' => $company->id,
            'city_id' => $city_id
        ]);
    }

    public function get(Company $company, string $city_id)
    {
        return $this->model->where(['company_id' => $company->id, 'city_id' => $city_id])->first();
    }

    public function cityHasArea(CompanyImmediateShippingCity $city, $area_id)
    {
        return $city->areas()->where('area_id', $area_id)->exists();
    }
}
