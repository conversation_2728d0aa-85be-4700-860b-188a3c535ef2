<?php

namespace App\Services\User;

use App\DTO\OrderDTO;
use App\Repositories\OrderRepository;
use App\Repositories\OrderItemRepository;

class OrderService
{
    public function __construct(
        private readonly OrderRepository $orderRepository,
        private readonly OrderItemRepository $orderItemRepository
    ) {
        //
    }

    private function generateShipmentNumber()
    {
        do {
            $number = strtoupper(substr(md5(uniqid()), 0, 8));
        } while ($this->orderItemRepository->shipmentNumberExists($number));

        return $number;
    }

    public function create(OrderDTO $dto): void
    {
        DB::transaction(function () use ($dto) {
            $data = $dto->toArray();
            $data['user_id'] = auth('user')->id();

            dd($data);

            $order = $this->orderRepository->create($data);

            foreach (request('items') as $item) {
                $item['shipment_number'] = $this->generateShipmentNumber();
                $item['order_id'] = $order->id;
                $this->orderItemRepository->create($item);
            }
        });
    }
}
