<?php

namespace Database\Seeders;

use App\Models\ShippingMethod;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ShippingMethodSeeder extends Seeder
{
    public function run(): void
    {
        $methods = ['lury', 'bike', 'motorcycle'];

        foreach ($methods as $method) {
            ShippingMethod::create([
                'name' => $method
            ]);
        }
    }
}
