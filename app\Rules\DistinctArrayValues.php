<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class DistinctArrayValues implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!is_array($value)) {
            return;
        }

        $seen = [];
        $duplicates = [];

        foreach ($value as $methodId) {
            if (isset($seen[$methodId])) {
                $duplicates[] = $methodId;
            } else {
                $seen[$methodId] = true;
            }
        }

        if (!empty($duplicates)) {
            $fail("Duplicate values found in {$attribute}.");
        }
    }
}
