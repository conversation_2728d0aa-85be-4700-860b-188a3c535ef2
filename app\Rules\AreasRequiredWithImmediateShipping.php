<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class AreasRequiredWithImmediateShipping implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Match something like: shipping_options.0.cities.1.id
        if (!preg_match('/shipping_options\.(\d+)\.cities\.(\d+)\.id/', $attribute, $matches)) {
            return;
        }

        [$full, $shippingIndex, $cityIndex] = $matches;

        // Get shipping type ID for this shipping option
        $shippingTypeId = request()->input("shipping_options.$shippingIndex.shipping_type_id");

        // Only validate areas if shipping type is 1 (immediate shipping)
        if ($shippingTypeId == 1) {
            $areas = request()->input("shipping_options.$shippingIndex.cities.$cityIndex.areas");

            if (empty($areas)) {
                $fail('Areas required in case of immediate shipping');
            }
        }
    }
}
