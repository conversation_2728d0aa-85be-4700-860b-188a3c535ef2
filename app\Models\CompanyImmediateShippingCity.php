<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CompanyImmediateShippingCity extends Model
{
    public $timestamps = false;

    protected $fillable = [
        'company_id',
        'city_id'
    ];


    public function city()
    {
        return $this->belongsTo(City::class);
    }

    public function areas()
    {
        return $this->belongsToMany(Area::class, 'company_immediate_shipping_city_areas', 'company_city_id', 'area_id');
    }
}
