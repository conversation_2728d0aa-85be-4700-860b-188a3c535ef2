<?php

namespace App\Traits;

use App\Models\Permission;
use App\Models\Role;

trait HasPermissions
{
    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    public function permissions()
    {
        return $this->morphToMany(Permission::class, 'model', 'model_permission', 'model_id', 'permission_id');
    }

    public function hasPermission($permission)
    {
        $permission = $this->permissions()->where('name', $permission)->first();

        if (!$permission) {
            return false;
        }

        return true;
    }
}
