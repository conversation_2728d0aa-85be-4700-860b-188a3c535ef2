<?php

namespace App\Http\Controllers\Driver;

use App\Http\Controllers\Controller;
use App\Http\Requests\Driver\CompleteProfileRequest;
use App\Http\Resources\Driver\LoginResource;
use App\Models\Driver;
use App\Repositories\CountryRepository;
use App\Repositories\OTPRepository;
use App\Services\DriverService;
use App\Services\MediaService;
use Carbon\Carbon;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\RateLimiter;

class RegistrationController extends Controller
{
    public function __construct(
        private DriverService $driverService,
        private OTPRepository $otpRepository,
        private CountryRepository $countryRepository,
        private MediaService $mediaService
    ) {}

    public function store(CompleteProfileRequest $request)
    {
        $phone = normalizeMobileNumber($request->phone);

        if (RateLimiter::tooManyAttempts("complete-driver-profile:$request->country_code-$phone", 4)) {
            throw new ThrottleRequestsException();
        }

        RateLimiter::hit("complete-driver-profile:$request->country_code-$phone", 60);

        $otp = $this->otpRepository->get(Driver::class, $request->country_code, $phone, $request->code);

        if (!$otp) {
            return errorMessage(__("invalid code"));
        }

        $latest_otp = $this->otpRepository->getLatest(Driver::class, $request->country_code, $phone);

        if ($latest_otp->code != $request->code) {
            return errorMessage(__('please enter the latest sent code'));
        }

        // expire code after 1 hour
        if (Carbon::parse($latest_otp->created_at)->addHour()->lt(now())) {
            return errorMessage(__('expired code'));
        }

        $data = $request->validated();
        $data['phone'] = $phone;

        DB::transaction(function () use ($data, $request, $phone) {
            $driver = $this->driverService->register($data);

            // delete all phone otp
            $this->otpRepository->deleteAll(Driver::class, $request->country_code, $phone);

            return $driver;
        });

        return successMessage("registration request created successfuly. please wait for approval");
    }
}
