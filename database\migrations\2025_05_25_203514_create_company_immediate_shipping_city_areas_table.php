<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('company_immediate_shipping_city_areas', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_city_id')->constrained('company_immediate_shipping_city_areas')->cascadeOnDelete();
            $table->foreignId('area_id')->constrained()->cascadeOnDelete();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('company_immediate_shipping_city_areas');
    }
};
