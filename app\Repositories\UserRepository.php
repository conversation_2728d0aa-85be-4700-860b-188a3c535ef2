<?php

namespace App\Repositories;

use App\Models\User;

class UserRepository
{
    public function __construct(private User $model) {}

    public function getByPhone($country_code, $phone)
    {
        return $this->model->where('country_code', $country_code)->where('phone', $phone)->first();
    }

    public function register(array $data)
    {
        return $this->model->create([
            'name' => $data['name'],
            'country_code' => $data['country_code'],
            'phone' => $data['phone'],
            'email' => $data['email'] ?? null,
            'gender' => $data['gender'] ?? null,
            'birth_date' => $data['birth_date'] ?? null,
            'last_login' => now(),
        ]);
    }
}
