<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('drivers', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('country_id')->constrained()->onDelete('cascade');
            $table->string('country_code');
            $table->string('phone')->unique();
            $table->string('email')->nullable()->unique();
            $table->date('birth_date')->nullable();
            $table->enum('gender', ['male', 'female', 'other'])->nullable();
            $table->foreignId('company_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('national_id')->unique();
            $table->string('bank');
            $table->string('bank_account_owner');
            $table->string('bank_account_number');
            $table->string('iban')->nullable();
            $table->boolean('is_available')->default(true);
            $table->boolean('push_notifications_enabled')->default(true);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('drivers');
    }
};
