<?php

namespace Database\Seeders;

use App\Models\ShippingType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ShippingTypeSeeder extends Seeder
{
    public function run(): void
    {
        $types = ['immediate', 'intercity', 'international'];

        foreach ($types as $type) {
            ShippingType::create([
                'name' => $type
            ]);
        }
    }
}
