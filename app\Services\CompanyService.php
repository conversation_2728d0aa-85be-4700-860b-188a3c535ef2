<?php

namespace App\Services;

use App\Repositories\AreaRepository;
use App\Repositories\CompanyAdminRepository;
use App\Repositories\CompanyImmediateShippingCityRepository;
use App\Repositories\CompanyRepository;
use App\Repositories\CompanyShippingTypeRepository;
use App\Repositories\CompanyShippingTypeSizeRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class CompanyService
{
    public function __construct(
        private CompanyRepository $companyRepository,
        private CompanyAdminRepository $companyAdminRepository,
        private MediaService $mediaService,
        private CompanyShippingTypeRepository $companyShippingTypeRepository,
        private CompanyShippingTypeSizeRepository $companyShippingTypeSizeRepository,
        private CompanyImmediateShippingCityRepository $companyImmediateShippingCityRepository,
        private AreaRepository $areaRepository
    ) {}

    public function register()
    {
        $data = request()->all();

        $this->validateRegistration();

        DB::transaction(function () use ($data) {

            $data['code'] = $this->generateCode();
            $data['phone'] = normalizeMobileNumber($data['phone']);

            $company = $this->companyRepository->register($data);

            if (request('commercial_registration_certificate')) {
                $this->mediaService->save($company, request('commercial_registration_certificate'), 'companies', 'commercial_registration_certificate');
            }

            if (request('cargo_insurance_certificate')) {
                $this->mediaService->save($company, request('cargo_insurance_certificate'), 'companies', 'cargo_insurance_certificate');
            }

            if (request('tax_certificate')) {
                $this->mediaService->save($company, request('tax_certificate'), 'companies', 'tax_certificate');
            }

            if (request('logo')) {
                $this->mediaService->save($company, request('logo'), 'companies', 'logo');
            }


            foreach (request('shipping_options') as $shipping_option) {
                $companyShippingType = $this->companyShippingTypeRepository->create(
                    $company,
                    $shipping_option['shipping_type_id'],
                    $shipping_option['has_express_delivery'] ?? null
                );

                foreach ($shipping_option['shipping_sizes'] as $shipping_size_id) {
                    $this->companyShippingTypeSizeRepository->create($companyShippingType, $shipping_size_id);
                }

                $companyShippingType->transportionMethods()->attach($shipping_option['transportion_methods']);

                // Immediate shipping
                if ($shipping_option['shipping_type_id'] == 1) {
                    foreach ($shipping_option['cities'] as $city) {
                        $companyImmediateShippingCity = $this->companyImmediateShippingCityRepository->create($company, $city['id']);
                        $companyImmediateShippingCity->areas()->attach($city['areas']);
                    }
                }

                // Intercity shipping
                if ($shipping_option['shipping_type_id'] == 2) {
                    $company->intercityShippingPickupCities()->attach($shipping_option['pickup_cities'], ['type' => 'pickup']);
                    $company->intercityShippingDeliveryCities()->attach($shipping_option['delivery_cities'], ['type' => 'delivery']);
                }

                // International shipping
                if ($shipping_option['shipping_type_id'] == 3) {
                    $company->internationalShippingCountires()->attach($shipping_option['shipping_countries']);
                }
            }


            // Creating Super Admin
            $data['company_id'] = $company->id;
            $data['admin_phone'] = normalizeMobileNumber($data['admin_phone']);
            $data['admin_password'] = bcrypt($data['admin_password']);
            $data['admin_role_id'] = 2;
            $data['is_super_admin'] = true;

            $this->companyAdminRepository->register($data);
        });
    }

    public function validateRegistration()
    {
        foreach (request('shipping_options') as $shipping_option) {

            // Immediate shipping
            if ($shipping_option['shipping_type_id'] == 1) {
                foreach ($shipping_option['cities'] as $city) {
                    foreach ($city['areas'] as $area) {
                        $area = $this->areaRepository->getById($area);

                        if ($area->city_id != $city['id']) {
                            throw new BadRequestHttpException("area id $area->id doesn't belong to city id $city[id]");
                        }
                    }
                }
            }
        }
    }

    public function generateCode()
    {
        do {
            $code = strtoupper(Str::random(10));
        } while ($this->companyRepository->getByCode($code));

        return $code;
    }
}
