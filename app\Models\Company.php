<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Company extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'name',
        'country_id',
        'country_code',
        'phone',
        'email',
        'business_registration_number',
        'bank',
        'bank_account_owner',
        'bank_account_number',
        'iban',
        'shipment_size'
    ];


    public function getPhoneAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function getEmailAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function getBusinessRegistrationNumberAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function drivers()
    {
        return $this->hasMany(Driver::class);
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function logo()
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'logo');
    }

    public function commercialRegistrationCertificate()
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'commercial_registration_certificate');
    }

    public function taxCertificate()
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'tax_certificate');
    }

    public function cargoInsuranceCertificate()
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'cargo_insurance_certificate');
    }

    public function companyAdmins()
    {
        return $this->hasMany(CompanyAdmin::class);
    }

    public function companyImmediateShippingCity()
    {
        return $this->belongsToMany(City::class, 'company_immediate_shipping_cities');
    }

    public function companyImmediateShippingArea()
    {
        return $this->belongsToMany(Area::class, 'company_immediate_shipping_areas');
    }

    public function companyIntercityShippingCity()
    {
        return $this->belongsToMany(City::class, 'company_intercity_shipping_cities');
    }

    public function companyInternationalShippingCountry()
    {
        return $this->belongsToMany(Country::class, 'company_international_shipping_countries');
    }

    public function companyInternationalShippingCity()
    {
        return $this->belongsToMany(City::class, 'company_international_shipping_cities');
    }
}
