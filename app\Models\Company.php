<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Company extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'code',
        'name',
        'country_code',
        'phone',
        'email',
        'address',
        'business_registration_number',
        'bank_name',
        'bank_account_owner',
        'bank_account_number',
        'iban'
    ];


    public function getPhoneAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function getEmailAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function getBusinessRegistrationNumberAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function drivers()
    {
        return $this->hasMany(Driver::class);
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function logo()
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'logo');
    }

    public function commercialRegistrationCertificate()
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'commercial_registration_certificate');
    }

    public function taxCertificate()
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'tax_certificate');
    }

    public function cargoInsuranceCertificate()
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'cargo_insurance_certificate');
    }

    public function superAdmin()
    {
        return $this->hasOne(CompanyAdmin::class)->where('is_super_admin', true);
    }

    public function shippingTypes()
    {
        return $this->hasMany(CompanyShippingType::class);
    }

    public function internationalShippingCountires()
    {
        return $this->belongsToMany(Country::class, 'company_international_shipping_countries', 'company_id', 'country_id');
    }

    public function intercityShippingPickupCities()
    {
        return $this->belongsToMany(City::class, 'company_intercity_shipping_cities', 'company_id', 'city_id')->where('type', 'pickup');
    }

    public function intercityShippingDeliveryCities()
    {
        return $this->belongsToMany(City::class, 'company_intercity_shipping_cities', 'company_id', 'city_id')->where('type', 'delivery');
    }

    public function immediateShippingCities()
    {
        return $this->hasMany(CompanyImmediateShippingCity::class, 'company_id');
    }

    public function internationalShippingCities()
    {
        return $this->belongsToMany(City::class, 'company_international_shipping_cities', 'company_id', 'city_id');
    }
}
